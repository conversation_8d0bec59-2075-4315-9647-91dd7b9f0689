using HCCB.Core.Models;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public interface ITaskManagementRepository
    {
        Task<List<TaskManagementTaskDto>> GetTasks(long companyId);
        Task<List<TaskDetails>> GetTaskForOutlets(long companyId, List<long> outletIds);
        Task<List<TaskDetails>> GetTaskForOutletsForTaskIds(long companyId, List<long> taskIds, bool includeDeactive = false);
    }
}
