using Hccb.Helpers;

namespace HCCB.DbStorage.DbModel.HccbDbModel
{
    public class TaskSummary
    {
        public long Id { get; set; }
        public long UserId { get; set; }
        public long PositionId { get; set; }
        public long DateKey { get; set; }
        public long CompanyId { get; set; }
        public short TotalTasks { get; set; }
        public short AchievedTasks { get; set; }
        public short MissedTasks { get; set; }
        public long TaskFocusAreaId { get; set; }
        public ICollection<TaskSummaryDetail> TaskSummaryDetails { get; set; } = new List<TaskSummaryDetail>();
    }

    public class TaskSummaryDetail
    {
        public long Id { get; set; }
        public long OutletId { get; set; }
        public long TaskSummaryId { get; set; }
        public long TaskId { get; set; }
        public TaskAchievementStatus TaskAchievementStatus { get; set; }
        public string? SearchText { get; set; }
        public int Segmentation { get; set; }
    }
}
