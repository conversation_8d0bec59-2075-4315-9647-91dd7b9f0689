﻿using Core.Models.DTOs;
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using System.Data.SqlClient;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class LocationsRepository : ILocationsRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;
        private readonly MasterDbSqlDataReader _masterDbSqlDataReader;

        public LocationsRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext, MasterDbSqlDataReader masterDbSqlDataReader)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
            _masterDbSqlDataReader=masterDbSqlDataReader;
        }

        public async Task<List<GeoHierarchy>> GetGeoHierarchy(long companyId, List<long> beatIds)
        {
            var query = $@"SELECT
                            b.Id as BeatId,
                            t.Id as TerritoryId, 
                            r.Id as RegionId,
                            z.Id as ZoneId,
                            l5.Id as Level5GeoId, 
                            l6.Id as Level6GeoId,
                            l7.Id as Level7GeoId 
                            from LocationBeats b
                            JOIN Territories t on t.Id = b.TerritoryId
                            JOIN Regions r on r.Id = t.RegionId
                            JOIN FACompanyZones z on z.Id = r.ZoneId
                            LEFT JOIN Geographies l5 on l5.Id = z.ParentId
                            LEFT JOIN Geographies l6 on l6.Id = l5.ParentId
                            LEFT JOIN Geographies l7 on l7.Id = l6.ParentId
                            Where b.Company = @companyId 
                            and t.CompanyId = @companyId
                            and r.CompanyId = @companyId
                            and z.Company = @companyId
                            AND (l5.CompanyId = @companyId OR l5.CompanyId IS NULL)
                            AND (l6.CompanyId = @companyId OR l6.CompanyId IS NULL)
                            AND (l7.CompanyId = @companyId OR l7.CompanyId IS NULL)
                            and b.Id in ($$Ids$$)";

            var sqlParameters = new List<SqlParameter> { new SqlParameter("companyId", companyId) };

            var response = await _masterDbSqlDataReader.GetModelFromQueryAsync<GeoHierarchy>(query, beatIds, parameters: sqlParameters, commandTimeout: 1200);

            return response.ToList();
        }

        public Task<List<LocationMinModel>> GetLocationMinModels(long companyId, IEnumerable<long> locationIds)
        {
            var locations = _readOnlyMasterDbContext.Locations.Where(x => x.CompanyId == companyId && locationIds.Contains(x.Id))
                .Select(x =>
                new LocationMinModel
                {
                    Id = x.Id,
                    IsFocused = x.IsFocused,
                    Segmentation = (int)x.Segmentation,
                    BeatId = x.BeatId ?? 0,
                    OutletCategory = x.OutletCategory,
                    OutletChannel = x.OutletChannel,
                }).ToList();

            return Task.FromResult(locations);
        }
    }
}
