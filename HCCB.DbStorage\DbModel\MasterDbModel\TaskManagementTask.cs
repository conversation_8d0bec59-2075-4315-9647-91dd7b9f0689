﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace HCCB.DbStorage.DbModel.MasterDbModel
{
    [Table("TaskManagementTasks")]
    public class TaskManagementTask
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("TaskManagementFocusAreaID")]
        public long? TaskManagementFocusAreaID { get; set; }

        [Column("TaskEntityType")]
        public FlexibleTargetEntityType? TaskEntityType { get; set; }

        [Column("TaskEntityId")]
        public long TaskEntityId { get; set; }

        [Column("TaskTarget")]
        public long? TaskTarget { get; set; }

        [Column("Sequence")]
        public long? Sequence { get; set; }

        [Column("ProductHierarchyLevel")]
        public ProductLevelHierarchy? ProductHierarchyLevel { get; set; }

        [Column("ProductHierarchyIds")]
        public string? ProductHierarchyIds { get; set; }

        [Column("CalculationMeasure")]
        public CalculationMeasure? CalculationMeasure { get; set; }

        [Column("Description")]
        public string? Description { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [Column("DeactivatedAt")]
        public DateTime? DeactivatedAt { get; set; }

        [Column("CreatedAt")]
        public DateTime? CreatedAt { get; set; }

        [Column("CreationContext")]
        public string? CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime? LastUpdatedAt { get; set; }

        [Column("RewardId")]
        public long? RewardId { get; set; }

        [Column("RewardQuantity")]
        public int? RewardQuantity { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("ProductSuggestedQtyList")]
        [StringLength(256)]
        public string? ProductSuggestedQtyList { get; set; }
        [ForeignKey("TaskManagementFocusAreaID")]
        public TaskManagementFocusArea TaskManagementFocusAreas { get; set; }
    }
}
