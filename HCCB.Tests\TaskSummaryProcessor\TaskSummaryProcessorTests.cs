using Microsoft.Extensions.DependencyInjection;
using HCCB.Tests.Configurations;
using HCCBTaskSummary;
using HCCBTaskSummary.Models;
using HCCBTaskSummary.Configuration;

namespace HCCB.Tests.Core
{
    [TestClass]
    public class CtsStatusProcessorTest
    {
        private ServiceProvider serviceProvider;

        [TestInitialize]
        public void Initialise()
        {
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3ManageWritable.vault.azure.net/");
            //Environment.SetEnvironmentVariable("KEYVAULT_ENDPOINT", "https://v3DebugWritable.vault.azure.net/");
            var configuration = Configuration.GetConfiguration();

            IServiceCollection serviceCollection = new ServiceCollection();
            Dependencies.SetUp(serviceCollection, configuration);
            serviceCollection.AddScoped<TaskSummaryProcessor>();
            serviceProvider = serviceCollection.BuildServiceProvider();
        }


        [TestMethod]
        public void TaskSummaryQueueProcessor()
        {
            var qp = serviceProvider.GetRequiredService<TaskSummaryProcessor>();
            var data = new TaskSummaryQueueData()
            {
                EmployeeId = 12450987,
                Date = DateTime.UtcNow,
                CompanyId = 193017
            };

            qp.ProcessTaskSummaryQueue(data).Wait();
        }
    }
}
