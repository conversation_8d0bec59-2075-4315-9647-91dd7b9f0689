﻿namespace Core.Models.DTOs
{
    public class ProductFlat
    {
        public long ProductId { get; set; }
        public long SecondaryCategoryId { get; set; }
        public long PrimaryCategoryId { get; set; }
        public long ProductDivisionId { get; set; }
        public long DisplayCategoryId { get; set; }
        public string AlternateCategory { get; set; }
        public string ProductAttributeText1 { get; set; }
        public string ProductAttributeText2 { get; set; }
    }
}