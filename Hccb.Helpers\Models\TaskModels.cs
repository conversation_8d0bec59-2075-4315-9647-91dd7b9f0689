﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCCB.Core.Models
{
    public class TaskModels
    {
        public long TaskId { get; set; }
        public long TaskEntityId { get; set; }
        public bool IsCompleted { get; set; }
        public long DateKey { get; set; }
        public long RouteId { get; set; }
    }
    public class TaskDetails
    {
        public long TaskId { get; set; }
        public long FocusAreaId { get; set; }
        public string FocusAreaName { get; set; }
        public string FocusAreaDescription { get; set; }
        public long OutletId { get; set; }
    }
    public class OutletTaskModel
    {
        public long OutletId { get; set; }
        public string OutletName { get; set; }
        public long RouteId { get; set; }
        public long QualifiedDate { get; set; }
    }
}
