﻿using Library.DateTimeHelpers;
using System.Text.Json;

namespace Core.Services
{
    public class MTDService(HttpClient httpClient)
    {
        public HttpClient _httpClient { get; } = httpClient;

        public async Task<FA_MTD_LMTD> GetDates(long compnayId, DateTime date, int yearStartMonth, bool includeToday = false)
        {
            var api = $"api/MTDLMTDDate/GetMTDLMTD?companyId={compnayId}&today={date.ToString("MM/dd/yyyy")}&includeToday={includeToday}&yearStartMonth={yearStartMonth}";
            var data = await _httpClient.GetAsync(api);

            data.EnsureSuccessStatusCode();

            var responseString = await data.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<FA_MTD_LMTD>(responseString);
        }
    }
}
