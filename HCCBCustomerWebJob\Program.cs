﻿using Azure.Storage.Blobs;
using HCCBCustomerWebJob.Configurations;
using HCCBCustomerWebJob.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Text;

namespace HCCBCustomerWebJob
{
    class Program
    {
        private const string connectionString = "DefaultEndpointsProtocol=https;AccountName=hccbdata;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
        private const string containerName = "secrets";
        private static string blobName;
        public static async Task Main(string[] args)
        {
            blobName = Environment.GetEnvironmentVariable("ConfigurationFileName");
            Console.WriteLine($"Loading Configuration File: {blobName}");
            // Reading base64 encoded file content from a blob
            var base64Content = await ReadBlobContentAsync(blobName);

            // Decoding base64 content
            var decodedContent = Convert.FromBase64String(base64Content);
            var jsonString = Encoding.UTF8.GetString(decodedContent);

            var builder = new HostBuilder()
               .ConfigureAppConfiguration((hostingContext, config) =>
               {
                   config.AddJsonFile("appsettings.json", optional: true);
                   config.AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(jsonString)))
                         .AddEnvironmentVariables();
               })
              .ConfigureServices((context, services) =>
              {
                  Dependencies.SetUp(services, context.Configuration);
              })
              .UseConsoleLifetime();

            var host = builder.Build();
            using (host)
            {
                var tasks = new List<Task>();

                #region Get Data from APIs
                if (Dependencies.currentEnvironment != "beta")
                {
                    tasks.Add(Task.Run(async () =>
                            {
                                using (var scope = host.Services.CreateScope())
                                {
                                    await scope.ServiceProvider.GetRequiredService<CustomerMasterSalesAreaProcessor>().Process();
                                }
                            }));
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<CustomerMasterProcessor>().Process();
                        }
                    }));
                    if (Dependencies.IsDailyRun())
                    {
                        tasks.Add(Task.Run(async () =>
                        {
                            using (var scope = host.Services.CreateScope())
                            {
                                await scope.ServiceProvider.GetRequiredService<UserMasterProcessor>().Process(); //Tested
                            }
                        }));
                        tasks.Add(Task.Run(async () =>
                        {
                            using (var scope = host.Services.CreateScope())
                            {
                                await scope.ServiceProvider.GetRequiredService<AssetMappingsGetter>().Process(); //Tested
                            }
                        }));
                    }
                    await Task.WhenAll(tasks);
                    tasks.Clear(); 
                }
                #endregion

                #region Process Positions, Geographies, UserMaster and Distributors
                if (Dependencies.IsDailyRun())
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<ProcessPositions>().Process(); //Tested 
                        }
                    }));
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<ProcessUserMaster>().Process(); //Tested
                        }
                    }));
                }
                tasks.Add(Task.Run(async () =>
                {
                    using (var scope = host.Services.CreateScope())
                    {
                        await scope.ServiceProvider.GetRequiredService<ProcessGeographies>().Process(); //Tested
                    }
                }));
                tasks.Add(Task.Run(async () =>
                {
                    using (var scope = host.Services.CreateScope())
                    {
                        await scope.ServiceProvider.GetRequiredService<ProcessDistributors>().Process(); //Tested
                    }
                }));
                tasks.Add(Task.Run(async () =>
                {
                    using (var scope = host.Services.CreateScope())
                    {
                        await scope.ServiceProvider.GetRequiredService<ProcessOutletMargins>().Process();
                    }
                }));
                await Task.WhenAll(tasks);
                tasks.Clear();
                #endregion

                #region Process Employees, PositionBeatMappings, DistributorBeatMappings, Outlets
                if (Dependencies.IsDailyRun())
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<ProcessEmployees>().Process(); //Tested
                        }
                    }));
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<ProcessPositionBeatMappings>().Process(); //Tested   
                        }
                    }));
                }
                tasks.Add(Task.Run(async () =>
                {
                    using (var scope = host.Services.CreateScope())
                    {
                        await scope.ServiceProvider.GetRequiredService<ProcessDistributorBeatMappings>().Process(); //Tested
                    }
                }));
                tasks.Add(Task.Run(async () =>
                {
                    using (var scope = host.Services.CreateScope())
                    {
                        await scope.ServiceProvider.GetRequiredService<ProcessOutlets>().Process();
                    }
                }));
                await Task.WhenAll(tasks);
                tasks.Clear();
                #endregion

                #region Process Asset Master
                if (Dependencies.IsDailyRun())
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        using (var scope = host.Services.CreateScope())
                        {
                            await scope.ServiceProvider.GetRequiredService<AssetMappingsProcessor>().Process(); //Tested
                        }
                    }));
                }
                await Task.WhenAll(tasks);
                tasks.Clear();
                #endregion
            }
        }

        public static async Task<string> ReadBlobContentAsync(string blobName)
        {
            BlobServiceClient blobServiceClient = new BlobServiceClient(connectionString);

            BlobContainerClient containerClient = blobServiceClient.GetBlobContainerClient(containerName);

            BlobClient blobClient = containerClient.GetBlobClient(blobName);

            using (var blobStream = await blobClient.OpenReadAsync())
            using (var reader = new StreamReader(blobStream))
            {
                return await reader.ReadToEndAsync();
            }
        }
    }
}