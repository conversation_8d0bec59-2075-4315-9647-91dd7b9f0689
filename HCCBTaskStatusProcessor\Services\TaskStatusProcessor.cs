﻿using HCCB.Core.Helpers;
using Library.DateTimeHelpers;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Core.Loggers;
using System.Diagnostics;
using Library.Infrastructure.QueueService;
using HCCBTaskStatusProcessor.Configurations;

namespace HCCBTaskStatusProcessor.Services
{
    public class TaskStatusProcessor
    {
        private readonly ISlackLogHelper slackLogHelper;
        private readonly TaskStatusService _taskStatusService;
        private readonly ITransactionRepository _transactionRepository;
        private readonly IFaMasterRepository _faMasterRepository;
        private readonly QueueHandlerService _queueHandlerService;
        private readonly FileLogger fileLogger;
        private string LogsDirectory = "Logs";
        private string TaskSummaryQueue = Dependencies.TaskSummaryQueue;

        public TaskStatusProcessor(TaskStatusService taskStatusService, ITransactionRepository transactionRepository, ISlackLog<PERSON><PERSON>per slackLogHelper, IFaMasterRepository faMasterRepository, QueueHandlerService queueHandlerService, FileLogger fileLogger)
        {
            this.slackLogHelper = slackLogHelper;
            _taskStatusService = taskStatusService;
            _transactionRepository = transactionRepository;
            _faMasterRepository = faMasterRepository;
            _queueHandlerService = queueHandlerService;
            this.fileLogger = fileLogger;
        }

        public async Task Process()
        {
            var sw = new Stopwatch();
            sw.Start();
            TimeZoneInfo istZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            var todayDate = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, istZone).AddDays(-1).Date;

            var logFileName = $"{LogsDirectory}{Path.DirectorySeparatorChar}TaskManagementProcessor_{todayDate:yyyyMMdd}_logs.txt";
            var logFile = new FileInfo(logFileName);
            var LogHeader = $"[{logFile.Name.Replace("_logs", "").Replace(".txt", "")}]: ";
            try
            {
                await fileLogger.InitializeAsync(logFileName, append: true, $"[TaskStatusProcessor]: ");
                fileLogger.WriteLine("HCCB Task Status Processor Started!");
                await slackLogHelper.SendMessageToSlack("HCCB Task Status Processor Started!");
                var processInfo = new List<TaskStatusProcess>
                {
                    new TaskStatusProcess { CompanyId = Dependencies.CompanyId }
                };

                var cutOffTime = DateTime.UtcNow.Date.AddMinutes(330); // cutoff time is 00:00 IST or 5:30 UTC

                foreach (var info in processInfo)
                {
                    var dateKeysToSync = await _transactionRepository.GetDateKeysForNewData(info.CompanyId, cutOffTime.AddDays(-1), cutOffTime);
                    foreach (var dateKey in dateKeysToSync)
                    {
                        fileLogger.WriteLine($"Processing tasks for DateKey: {dateKey} for CompanyId: {info.CompanyId}");
                        await _taskStatusService.ProcessTasks(info.CompanyId, DateTimeExtentions.FromDateKey((int)dateKey));
                    }
                }

                // add employees to task summary queue
                var todaysDate = DateTime.UtcNow.AddDays(-1).Date.AddMinutes(330);
                foreach (var info in processInfo)
                {
                    var activeEmployeeIds = await _faMasterRepository.GetActiveEmployeeIds(info.CompanyId);
                    fileLogger.WriteLine($"Adding {activeEmployeeIds.Count} active employees to task-summary-queue for CompanyId: {info.CompanyId}");
                    
                    foreach (var employeeId in activeEmployeeIds)
                    {
                        var queueData = new { EmployeeId = employeeId, Date = todaysDate.Date, CompanyId = info.CompanyId };
                        await _queueHandlerService.AddToQueue(TaskSummaryQueue, queueData);
                    }
                    fileLogger.WriteLine($"Successfully queued {activeEmployeeIds.Count} employees for task achievement processing");
                }

                await slackLogHelper.SendMessageToSlack("HCCB Task Status Processor Completed");
                fileLogger.WriteLine("HCCB Task Status Processor Completed");
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine($"Error in  HCCB Task Status Processor: {ex.Message}!");
                await slackLogHelper.SendCustomSlackMessage("Error in Task Status Processor", false, ex.Message.ToString(), exception: ex.GetBaseException());
            }
            finally
            {
                await slackLogHelper.SendMessageToSlack("Ending Task Status Processor!");
                var logMessage = $"Finished processing in {sw.Elapsed.TotalSeconds}.";
                fileLogger.WriteLine(logMessage);
                fileLogger.Dispose();
                if (await slackLogHelper.SendLogAndFile(logFile, $"{LogHeader}\n" + logMessage))
                {
                    logFile.Delete();
                }
            }
        }
    }

    public class TaskStatusProcess
    {
        public long CompanyId { get; set; }

        public List<long> FoucsAreaIds { get; set; }
    }
}
