﻿using HCCBTasksManagementProcessor.Models;

namespace HCCBTasksManagementProcessor.Repositories
{
    public interface ITaskMasterRepository
    {
        Task<List<RecommendedProduct>> GetTag2RecommendedProducts(long companyId);
        Task CreateTasks(List<TaskManagementTask> tasksCreateList);
        Task DeactivateTasks(List<long> tasksId);
        Task InsertOutletTagDetails(List<Location> updateList);
        Task DeleteRecommendedProducts(List<long> recommendedProducts);
        Task AddRecommendedProducts(List<RecommendedProduct> recommendedProducts);
        Task UpdateProcessedTasks(List<long> tasksIds);

    }
}
