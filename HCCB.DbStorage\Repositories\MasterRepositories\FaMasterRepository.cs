﻿using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.DbModel.MasterDbModel;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class FaMasterRepository : IFaMasterRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;

        public FaMasterRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
        }

#pragma warning disable CS8602 // Dereference of a possibly null reference.
        public async Task<Dictionary<string, long>> GetEmployeesAsync(long companyId)
        {
            return (await _readOnlyMasterDbContext.Employees.Where(w => w.CompanyId == companyId).ToListAsync())
                .Where(w => !string.IsNullOrEmpty(w.ErpId)).GroupBy(p => p.ErpId, (k, g) => g.First()).ToDictionary(p => p.ErpId.ToString(), p => p.Id);
        }

        public async Task<string> GetEmployeeErpById(long id)
        {
            return await _readOnlyMasterDbContext.Employees.Where(e => id == e.Id).Select(e => e.ErpId).FirstOrDefaultAsync();
        }
        public async Task<List<long>> GetPositionIdsForUserId(long userId, long companyId)
        {
            return await _readOnlyMasterDbContext.PositionCodeEntityMappings.Where(d => d.CompanyId == companyId && !d.IsDeactive && d.EntityId == userId).Select(d => d.PositionCodeId).Distinct().ToListAsync();
        }
        public async Task<List<long>> GetActiveEmployeeIds(long companyId)
        {
            return await _readOnlyMasterDbContext.Employees.Where(e => e.CompanyId == companyId && !e.Deleted && !e.IsDeactive).Select(e => e.Id).ToListAsync();
        }
        public async Task<Dictionary<string, long>> GetDistributorsAsync(long companyId)
        {
            return (await _readOnlyMasterDbContext.Distributors.Where(w => w.CompanyId == companyId).ToListAsync())
                .Where(w => !string.IsNullOrEmpty(w.ErpId)).GroupBy(p => p.ErpId, (k, g) => g.First()).ToDictionary(p => p.ErpId.ToString(), p => p.Id);
        }

        public async Task<string> GetAssetErpIdByAssetMappingId(long assetMappingId, long companyId)
        {
            var asset = await _readOnlyMasterDbContext.AssetOutletMappings
                            .Where(mapping => mapping.Id == assetMappingId && mapping.CompanyId == companyId)
                            .Select(mapping => mapping.AssetDefinitions)
                            .FirstOrDefaultAsync();
            return asset?.ErpId ?? string.Empty;
        }

        public async Task<Dictionary<string, long>> GetLocationsAsync(long companyId)
        {
            return (await _readOnlyMasterDbContext.Locations.Where(w => w.CompanyId == companyId).Select(l => new { l.ErpId, l.Id }).ToListAsync())
                .Where(w => !string.IsNullOrEmpty(w.ErpId)).GroupBy(p => p.ErpId, (k, g) => g.First()).ToDictionary(p => p.ErpId.ToString(), p => p.Id);
        }

        public async Task<List<Location>> GetOutletsByIds(List<long> list)
        {
            return await _readOnlyMasterDbContext.Locations.Where(x => list.Contains(x.Id)).ToListAsync();
        }
        public async Task<Dictionary<string, Product>> GetProductsAsync(long companyId)
        {
            return (await _readOnlyMasterDbContext.Products.Where(w => w.CompanyId == companyId).ToListAsync())
                .Where(w => !string.IsNullOrEmpty(w.ErpId)).GroupBy(p => p.ErpId, (k, g) => g.First()).ToDictionary(p => p.ErpId.ToString(), p => p);
        }
#pragma warning restore CS8602 // Dereference of a possibly null reference.
        public async Task<List<string>> GetActiveOutletMarginNames(long companyId)
        {
            return await _readOnlyMasterDbContext.EntityMarginSlabs.Where(w => w.CompanyId == companyId && w.Deleted == false).Select(x => x.Name).ToListAsync();
        }
        public async Task<string> GetAssetDefinitionErpIdByName(string assestName, long companyId)
        {
            return await _readOnlyMasterDbContext.AssetDefinitions.Where(a => a.CompanyId == companyId && a.Name == assestName).Select(b => b.ErpId.ToString()).FirstOrDefaultAsync();
        }
    }
}
