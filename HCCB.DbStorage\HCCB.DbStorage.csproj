﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EFCore.BulkExtensions" Version="8.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\FA_Libraries\EntityHelper\EntityHelper.csproj" />
    <ProjectReference Include="..\FA_Libraries\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\FA_Libraries\Libraries.CommonModels\Libraries.CommonModels.csproj" />
    <ProjectReference Include="..\FA_Libraries\Library.SqlHelper\Library.SqlHelper.csproj" />
    <ProjectReference Include="..\HCCB-ADLS.DbStorage\HCCB_ADLS.DbStorage.csproj" />
    <ProjectReference Include="..\Hccb.Helpers\HCCB.Core.csproj" />
  </ItemGroup>

</Project>
