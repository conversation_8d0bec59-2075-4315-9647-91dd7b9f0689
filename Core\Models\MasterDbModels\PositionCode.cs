﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Models.MasterDbModels
{
    [Table("PositionCodes")]
    public class PositionCode 
    {
        public long CompanyId { get; set; }
        public bool Deleted { get; set; }
        public long Id { get; set; }
        public PositionCodeLevel Level { get; set; }
        public string Name { get; set; }
        public long? ParentId { get; set; }
    }
}
