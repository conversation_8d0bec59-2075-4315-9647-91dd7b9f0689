﻿using HCCBCustomerWebJob.Repositories;
using HCCBCustomerWebJob.Helpers;
using Microsoft.IdentityModel.Tokens;
using HCCBCustomerWebJob.Models;
using Library.ResponseHelpers;
using Core.Loggers;
using SlackNet;
using HCCBCustomerWebJob.Configurations;
using Core.Models.HccbDbModels;
using Core.Repositories;
using Core.Models;
using Core.Abstracts;

namespace HCCBCustomerWebJob.Services
{
    public class ProcessUserMaster(IUserMasterRepository userMasterRepository, IFAEmployeeService fAEmployeeService, HCCBApiValues hCCBApiValues,
        ISlackLogHelper slackLogHelper, FileLogger fileLogger,
        IHccbIntegrationLogsRepository hccbIntegrationRepository, IRoutePlanRepository routePlanRepository) : ProcessorBaseIntegrationLogs(fileLogger, slackLogHelper, hccbIntegrationRepository)
    {
        private readonly IUserMasterRepository userMasterRepository = userMasterRepository;
        private readonly IFAEmployeeService fAEmployeeService = fAEmployeeService;
        private readonly HCCBApiValues hCCBApiValues = hCCBApiValues;
        private readonly IRoutePlanRepository routerRepository = routePlanRepository;

        protected override List<IntegratationSummary> _summary { get; set; }
        protected override List<EntityEnum> IntegrationEntities => new List<EntityEnum> { EntityEnum.UserDeactivation, EntityEnum.UserReactivation };

        private const string ProcessorName = "ProcessUserMaster processor";
        protected override string LogHeader
        {
            get; set;
        }

        protected override string GetProcessorName()
        {
            return ProcessorName;
        }

        protected override string GetChannelId()
        {
            return Dependencies.ChannelId;
        }

        protected override async Task _Process(params object?[] args)
        {
            var time = DateTime.UtcNow;
            long companyId = hCCBApiValues.companyId;

            // Get employees and process statuses
            var allEmployees = await fAEmployeeService.GetAllEmployee();
            var faActiveSet = new HashSet<string>();
            var faInactiveSet = new HashSet<string>();

            foreach (var group in allEmployees.GroupBy(x => x.UserErpId))
            {
                bool hasActive = false;
                foreach (var emp in group)
                {
                    if (emp.UserStatus == "Active")
                    {
                        hasActive = true;
                        break;
                    }
                }
                if (hasActive) faActiveSet.Add(group.Key);
                else faInactiveSet.Add(group.Key);
            }

            //Get HCCB Data
            var getHccbData = await userMasterRepository.GetUserMaster(companyId);

            // Directly identify users to activate/deactivate
            var userErpIdsToBeDeactivated = getHccbData
                .Where(x => faActiveSet.Contains(x.marketarea_id) &&
                           x.sfa_active.HasValue &&
                           x.sfa_active.Value == 0)
                .Select(e => e.marketarea_id)
                .ToList();

            var userErpIdsToBeActivated = getHccbData
                .Where(x => faInactiveSet.Contains(x.marketarea_id) &&
                           x.sfa_active.HasValue &&
                           x.sfa_active.Value == 1)
                .Select(e => new EmployeePositionDetails
                {
                    UserErpId = e.marketarea_id,
                    PositionCode = e.marketarea_id
                })
                .ToList();
            var deactIntegrationId = GetIntegrationLog(IntegrationEntities[0]).Id;
            var reactIntegrationId = GetIntegrationLog(IntegrationEntities[1]).Id;

            // Process deactivations
            var deactivateUserResponses = userErpIdsToBeDeactivated.Count > 0
                ? await fAEmployeeService.DeactivateEmployees(userErpIdsToBeDeactivated, deactIntegrationId)
                : new List<ActivateDeactivateUserResponse>();

            // Process activations
            var activateUserResponses = userErpIdsToBeActivated.Count > 0
                ? await fAEmployeeService.ReactivateEmployees(userErpIdsToBeActivated, reactIntegrationId)
                : new List<ActivateDeactivateUserResponse>();

            // Calculate deactivation stats
            int deactSuccess = 0, deactIgnored = 0;
            foreach (var resp in deactivateUserResponses)
            {
                if (resp.ResponseStatus == ResponseStatus.Success) deactSuccess++;
                else if (resp.ResponseStatus == ResponseStatus.Ignored) deactIgnored++;
            }
            int deactFailed = deactivateUserResponses.Count - deactSuccess - deactIgnored;

            var deactivationStats = new IntegratationSummary
            {
                TotalReceived = userErpIdsToBeDeactivated.Count,
                Successful = deactSuccess,
                Ignored = deactIgnored,
                Failed = deactFailed
            };

            // Send Deactivation Summary
            string deactivationLogHeader = $"{LogHeader} - Deactivation Summary";
            await _slackLogHelper.SendCustomSlackMessage(
                deactivationLogHeader,
                true,
                "",
                totalReceived: deactivationStats.TotalReceived,
                totalUpdated: deactivationStats.Successful,
                totalFailed: deactivationStats.Failed,
                totalIgnored: deactivationStats.Ignored
            );

            // Calculate activation stat
            int reactSuccess = 0, reactIgnored = 0;
            foreach (var resp in activateUserResponses)
            {
                if (resp.ResponseStatus == ResponseStatus.Success) reactSuccess++;
                else if (resp.ResponseStatus == ResponseStatus.Ignored) reactIgnored++;
            }
            int reactFailed = activateUserResponses.Count - reactSuccess - reactIgnored;

            var reactivationStats = new IntegratationSummary
            {
                TotalReceived = userErpIdsToBeActivated.Count,
                Successful = reactSuccess,
                Ignored = reactIgnored,
                Failed = reactFailed
            };

            // Send Activation Summary
            string activationLogHeader = $"{LogHeader} - Activation Summary";
            await _slackLogHelper.SendCustomSlackMessage(
                activationLogHeader,
                true,
                "",
                totalReceived: reactivationStats.TotalReceived,
                totalUpdated: reactivationStats.Successful,
                totalFailed: reactivationStats.Failed
            );
            _summary = new List<IntegratationSummary> { deactivationStats, reactivationStats };

            //Deactive RoutePlans
            await routerRepository.UpdateRoutePlanEndDatesAsync(companyId);

            // Combine both responses
            var allResponses = deactivateUserResponses.Concat(activateUserResponses);

            // Filter ERPIds with ResponseStatus as Success or Ignored
            var updatedErpIds = new HashSet<string>(
                allResponses
                    .Where(r => r.ResponseStatus == ResponseStatus.Success || r.ResponseStatus == ResponseStatus.Ignored)
                    .Select(r => r.ERPId)
            );
            var hccbMarketAreaIds = getHccbData
                .Where(x => updatedErpIds.Contains(x.marketarea_id))
                .Select(x => x.marketarea_id)
                .ToList();

            await userMasterRepository.ProcessedUsers(companyId, hccbMarketAreaIds);
        }

        protected override long GetCompanyId()
        {
            return hCCBApiValues.companyId;
        }

        protected override IntegrationEnum GetIntegrationEnum()
        {
            return IntegrationEnum.CustomerMaster;
        }

        protected override void _ValidateArguments(params object[] args)
        {
            return;
        }
    }
}
