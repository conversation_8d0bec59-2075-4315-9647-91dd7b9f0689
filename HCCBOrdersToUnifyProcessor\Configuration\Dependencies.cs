using Core.Loggers;
using Core.Repositories;
using Core.Services;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.Repositories.HccbRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SlackNet;
using SlackNet.Extensions.DependencyInjection;
using System.Net.Http.Headers;

namespace HCCBOrdersToUnifyProcessor.Configuration
{
    public static class Dependencies
    {
        public const int YearStartMonth = 1;
        public static string ChannelId = "C08D4PSBW4V"; // Default channel ID
        public static long CompanyId; // Default company IDs

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            ChannelId = config.GetValue<string>("Slack:ChannelId") ?? ChannelId;
            CompanyId = config.GetValue<long>("CompanyId");
            
            // Add HCCB Database Context
            services.AddDbContext<WritableHccbDataContext>(options =>
            {
                options.UseSqlServer(config.GetConnectionString("HCCBDbConnectionString"), SqlResiliencyBuilder);
            });

            // Add Master Database Context
            services.AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(config.GetConnectionString("ReadOnlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            
            // Add Master Data Reader
            services.AddSingleton<MasterDbSqlDataReader>(sp =>
            {
                var connectionString = config.GetConnectionString("ReadOnlyMasterDbConnectionString");
                return new MasterDbSqlDataReader(connectionString, timeout: 500);
            });

            // Add ClickHouse Data Reader
            services.AddSingleton<HCCBUnifyClickhouseSqlDataReader>(sp =>
            {
                var connectionString = config.GetConnectionString("ClickHouseConnectionString");
                var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
                return new HCCBUnifyClickhouseSqlDataReader(connectionString, httpClientFactory);
            });

            // Add Slack integration
            services.AddSlackNet(c => c
                .UseApiToken("******************************************************"));
            
            services.AddSingleton<Processor>();
            services.AddHttpClient();
            services.AddHttpClient<MTDService>(client =>
            {
                client.BaseAddress = new Uri(config.GetValue<string>("ReportingApi:BaseUrl"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",config.GetValue<string>("ReportingApi:Token"));
            });
            
            // Configure HTTP client for ClickHouse
            services.AddHttpClient(HCCBUnifyClickhouseSqlDataReader.SuggestedHttpClientName)
                .ConfigurePrimaryHttpMessageHandler(_ => new HttpClientHandler
                {
                    AutomaticDecompression = (System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate)
                });
            
            services.AddSingleton<ISlackLogHelper>(sp => new SlackLogHelperV2(sp.GetRequiredService<IHttpClientFactory>(), 
                sp.GetRequiredService<ISlackApiClient>(), null, ChannelId));
            
            services.AddScoped<FileLogger>();
            services.AddScoped<IPositionCodeRepository, PositionCodeRepository>();
            services.AddScoped<ILocationsRepository, LocationsRepository>();
            services.AddScoped<IProductsRepository, ProductsRepository>();
            services.AddScoped<ILastSyncTimeRepository, LastSyncTimeRepository>();
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder options)
        {
            options.CommandTimeout(600);
            options.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
        }
    }
}
