using HCCBTasksManagementProcessor.Helpers;
using HCCBTasksManagementProcessor.Models;
using HCCBTasksManagementProcessor.Repositories;
using HCCBTasksManagementProcessor.DbContexts;
using Microsoft.EntityFrameworkCore;
using Core.Loggers;
using Core.Abstracts;
using HCCBTasksManagementProcessor.Configurations;

namespace HCCBTasksManagementProcessor.Services;

public class TaskMasterProcessorService(
        ISlackLogHelper slackLogHelper, HCCBFaDbContext hccbDbContext, TaskMasterService taskMasterService,
        ITaskMasterRepository taskMasterRepository, 
        AppConfigSettings appConfigSettings, FileLogger fileLogger) : ProcessorBase(fileLogger, slackLogHelper)
{
    private readonly ITaskMasterRepository taskMasterRepository = taskMasterRepository;
    private readonly TaskMasterService taskMasterService = taskMasterService;
    private readonly HCCBFaDbContext hccbDbContext = hccbDbContext;
    private readonly AppConfigSettings appConfigSettings = appConfigSettings;
    private const string processorName = "TaskManagementProcessor";
    private long CompanyId = Dependencies.CompanyId;
    protected override string GetProcessorName() => processorName;

    protected override string GetChannelId() => Dependencies.ChannelId;

    protected override string LogHeader { get; set; }

    protected override async Task _Process(params object?[] args)
    {
        var today = appConfigSettings.Date != null ? appConfigSettings.Date : DateTime.UtcNow.AddMinutes(330).Date;
        var todaysTasks = await hccbDbContext.HCCBTaskManagementProcessorData.Where(t => t.IsProcessed == false && t.StartDate.Date == today && t.CompanyId == CompanyId).ToListAsync();
        if (todaysTasks.Count() != 0)
        {
            fileLogger.WriteLine($"Processing tasks for Company ID: {CompanyId}. Received {todaysTasks.Count} records.");
            await _slackLogHelper.SendMessageToSlack($"Processing tasks for Company ID: {CompanyId}. Received {todaysTasks.Count} records.");

            await taskMasterService.ProcessTasks(todaysTasks);
        }
        else
        {
            fileLogger.WriteLine($"Unfortunately No Tasks to Create/Update today!");
            await _slackLogHelper.SendMessageToSlack("Unfortunately No Tasks to Create/Update today!");
        }
    }

    protected override void _ValidateArguments(params object?[] args)
    {
        // Implement argument validation logic if needed
    }
}
