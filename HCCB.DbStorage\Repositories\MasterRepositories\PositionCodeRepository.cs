﻿using Core.Models;
using Core.Models.MasterDbModels;
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using Libraries.CommonEnums;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class PositionCodeRepository : IPositionCodeRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;

        public PositionCodeRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
        }

        public Task<List<PositionUserHierarchy>> GetPositionUserHierarchies(long companyId)
        {
            var allPositionMappings = _readOnlyMasterDbContext.PositionCodeEntityMappings.Where(x => x.CompanyId == companyId && !x.IsDeactive)
                .ToDictionary(m => m.PositionCodeId, m => m.EntityId);
            
            var allPositions = _readOnlyMasterDbContext.PositionCodes
                .Where(x => x.CompanyId == companyId && !x.Deleted)
                .ToDictionary(x => x.Id, p => p);

            var responseList = new List<PositionUserHierarchy>(allPositions.Count);

            foreach (var item in allPositions)
            {
                var response = new PositionUserHierarchy();
                response.Level = item.Value.Level;
                response.PositionName = item.Value.Name;
                response = UpdateHierarchy(response, item.Value, level: 1, allPositionMappings, allPositions);
                responseList.Add(response);
            }

            return Task.FromResult(responseList);
        }

        private PositionUserHierarchy UpdateHierarchy(PositionUserHierarchy response, PositionCode position, int level, Dictionary<long, long> allPositionMappings, Dictionary<long, PositionCode> allPositions)
        {
            AssignPositionAndUser(response, level, position.Id, allPositionMappings.TryGetValue(position.Id, out long value) ? value : 0);

            if (level < GetPositionLevelInt(response.Level))
            {
                return UpdateHierarchy(response, position, level + 1, allPositionMappings, allPositions);
            }
            else if (position.ParentId.HasValue && allPositions.TryGetValue(position.ParentId.Value, out var parentPosition))
            {
                return UpdateHierarchy(response, parentPosition, level + 1, allPositionMappings, allPositions);
            }

            return response;
        }

        private int GetPositionLevelInt(PositionCodeLevel level)
        {
            switch (level)
            {
                case PositionCodeLevel.L8Position:
                    return 8;
                case PositionCodeLevel.L7Position:
                    return 7;
                case PositionCodeLevel.L6Position:
                    return 6;
                case PositionCodeLevel.L5Position:
                    return 5;
                case PositionCodeLevel.L4Position:
                    return 4;
                case PositionCodeLevel.L3Position:
                    return 3;
                case PositionCodeLevel.L2Position:
                    return 2;
                case PositionCodeLevel.L1Position:
                    return 1;
                default:
                    return 0;
            }
        }

        private void AssignPositionAndUser(PositionUserHierarchy response, int level, long positionId, long userId)
        {
            switch (level)
            {
                case 1:
                    response.PositionId = positionId;
                    response.UserId = userId;
                    break;
                case 2:
                    response.PositionLevel2Id = positionId;
                    response.UserLevel2Id = userId;
                    break;
                case 3:
                    response.PositionLevel3Id = positionId;
                    response.UserLevel3Id = userId;
                    break;
                case 4:
                    response.PositionLevel4Id = positionId;
                    response.UserLevel4Id = userId;
                    break;
                case 5:
                    response.PositionLevel5Id = positionId;
                    response.UserLevel5Id = userId;
                    break;
                case 6:
                    response.PositionLevel6Id = positionId;
                    response.UserLevel6Id = userId;
                    break;
                case 7:
                    response.PositionLevel7Id = positionId;
                    response.UserLevel7Id = userId;
                    break;
                case 8:
                    response.PositionLevel8Id = positionId;
                    response.UserLevel8Id = userId;
                    break;
                default:
                    break;
            }
        }
    }
}
