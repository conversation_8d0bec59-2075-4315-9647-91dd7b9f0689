using Core.Loggers;
using Hccb.Helpers;
using HCCB.Core.Models;
using HCCB.DbStorage.DbModel.HccbDbModel;
using HCCB.DbStorage.Repositories.HccbRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using HCCBTaskSummary.Models;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;

namespace HCCBTaskSummary.Services
{
    public class TaskSummaryService
    {
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly ITransactionRepository _transactionRepository;
        private readonly IFaMasterRepository _faMasterRepository;
        private readonly IRouteRepository _routeRepository;
        private readonly ITaskManagementRepository _taskManagementRepository;
        private readonly IHccbTaskRepository _hccbTasksRepository;

        public TaskSummaryService(
            ISlackLogHelper slackLogHelper, 
            ITransactionRepository transactionRepository, 
            IFaMasterRepository faMasterRepository,
            IRouteRepository routeRepository,
            ITaskManagementRepository taskManagementRepository,
            IHccbTaskRepository hccbTasksRepository)
        {
            _slackLogHelper = slackLogHelper;
            _transactionRepository = transactionRepository;
            _routeRepository = routeRepository;
            _taskManagementRepository = taskManagementRepository;
            _hccbTasksRepository = hccbTasksRepository;
            _faMasterRepository = faMasterRepository;
        }

        public async Task ProcessTaskSummary(long employeeId, DateTime date, long companyId)
        {            
            try
            {
                Console.WriteLine($"Processing task summary for EmployeeId: {employeeId} and Date: {date:yyyy-MM-dd}");
                // fetch outlets of route plan of employee
                var positionIds = await _faMasterRepository.GetPositionIdsForUserId(employeeId, companyId);
                var positionId = positionIds.FirstOrDefault();
                var outletData = await GetOutletDataDict(companyId, date, employeeId);

                var taskLiveData = await _hccbTasksRepository.GetTaskForEmployee(companyId, employeeId, date);

                var outletIdsFromTasks = taskLiveData.Select(s => s.TaskEntityId);
                var outletIdsFromRoutes = outletData.Select(s => s.OutletId);
                var outletIds = outletIdsFromTasks.Union(outletIdsFromRoutes).ToList();

                var outlets = await _faMasterRepository.GetOutletsByIds(outletIds);
                var outletDictionary = outlets.ToDictionary(x => x.Id, x => x);

                var (outletTaskDict, taskWiseTaskData) = await GetTaskDataForOutlets(outletIds, companyId);

                var tasksFromLiveData = taskLiveData.Select(s => s.TaskId).ToHashSet();
                var allTasks = taskWiseTaskData.Keys.ToHashSet();
                var taskNotInvolved = tasksFromLiveData.Where(s => !allTasks.Contains(s)).ToList();

                if (taskNotInvolved.Any())
                {
                    var (taskMasterForNotInvolvedTask_outletWise, taskMasterForNotInvolvedTask_taskWise) = await GetTaskDataForOutOfRouteOutletVisits(taskNotInvolved, companyId);
                    taskWiseTaskData = taskWiseTaskData.Concat(taskMasterForNotInvolvedTask_taskWise)
                             .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                    foreach (var item in taskMasterForNotInvolvedTask_outletWise)
                    {
                        if (outletTaskDict.TryGetValue(item.Key, out var existingTasks))
                        {
                            existingTasks.AddRange(item.Value);
                        }
                        else
                        {
                            outletTaskDict[item.Key] = item.Value;
                        }
                    }
                }

                int achievedTasks = 0;
                int missedTasks = 0;
                var taskDetailsList = new List<TaskSummaryItem>();
                var isOutletVisited = outletIds.ToDictionary(s => s, s => false);
                
                foreach (var task in taskLiveData)
                {
                    if (taskWiseTaskData.TryGetValue(task.TaskId, out var taskMaster))
                    {
                        isOutletVisited[task.TaskEntityId] = true;
                        
                        if (task.IsCompleted) {
                            achievedTasks++;
                        } else {
                            missedTasks++;
                        }
                            
                        outletDictionary.TryGetValue(task.TaskEntityId, out var outlet);
                        
                        taskDetailsList.Add(new TaskSummaryItem
                        {
                            OutletId = task.TaskEntityId,
                            TaskId = task.TaskId,
                            TaskFocusAreaId = taskMaster.FocusAreaId,
                            TaskAchievementStatus = task.IsCompleted ? TaskAchievementStatus.Achieved : TaskAchievementStatus.Missed,
                            SearchText = outlet?.ShopName ?? "Name not found",
                            Segmentation = (int)(outlet?.Segmentation ?? 0)
                        });
                    }
                }

                var notVisitedOutlets = outletData.Where(s => isOutletVisited.ContainsKey(s.OutletId) && !isOutletVisited[s.OutletId]).ToList();
                var notVisitedTasks = notVisitedOutlets
                    .Where(outlet => outletTaskDict.ContainsKey(outlet.OutletId))
                    .SelectMany(outlet => outletTaskDict[outlet.OutletId]
                        .Select(task => 
                        {
                            var outletDetail = outletDictionary.ContainsKey(outlet.OutletId) ? outletDictionary[outlet.OutletId] : null;
                            return new TaskSummaryItem
                            {
                                OutletId = outlet.OutletId,
                                TaskId = task.TaskId,
                                TaskFocusAreaId = task.FocusAreaId,
                                TaskAchievementStatus = TaskAchievementStatus.NotVisited,
                                SearchText = outletDetail?.ShopName ?? "Name not found",
                                Segmentation = outletDetail != null ? (int)outletDetail.Segmentation : 0
                            };
                        }))
                    .ToList();
                    
                taskDetailsList.AddRange(notVisitedTasks);
                
                var tasksByFocusArea = taskDetailsList.GroupBy(t => t.TaskFocusAreaId);
                
                var taskSummaryModels = new List<TaskSummary>();
                
                foreach (var group in tasksByFocusArea)
                {
                    long focusAreaId = group.Key;
                    var focusAreaTasks = group.ToList();
                    
                    int focusAreaAchieved = focusAreaTasks.Count(t => t.TaskAchievementStatus == TaskAchievementStatus.Achieved);
                    int focusAreaMissed = focusAreaTasks.Count(t => t.TaskAchievementStatus == TaskAchievementStatus.Missed);
                    
                    var userTaskSummaryModel = new TaskSummary
                    {
                        UserId = employeeId,
                        PositionId = positionId,
                        DateKey = date.GetDateKey(),
                        CompanyId = companyId,
                        AchievedTasks = (short)focusAreaAchieved,
                        MissedTasks = (short)focusAreaMissed,
                        TotalTasks = (short)focusAreaTasks.Count,
                        TaskFocusAreaId = focusAreaId,
                        TaskSummaryDetails = focusAreaTasks.Select(item => new TaskSummaryDetail
                        {
                            OutletId = item.OutletId,
                            TaskId = item.TaskId,
                            TaskAchievementStatus = item.TaskAchievementStatus,
                            SearchText = item.SearchText,
                            Segmentation = item.Segmentation
                        }).ToList()
                    };
                    
                    taskSummaryModels.Add(userTaskSummaryModel);
                }
                
                await _hccbTasksRepository.SaveTaskSummaryWithDetails(taskSummaryModels);
                    
                Console.WriteLine($"Task summary processing completed for EmployeeId: {employeeId} and Date: {date:yyyy-MM-dd}.");
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing task summary for EmployeeId: {employeeId}, Date: {date:yyyy-MM-dd}, CompanyId: {companyId}. Error: {ex.Message}";
                Console.WriteLine(errorMessage);
                await _slackLogHelper.SendCustomSlackMessage("Task Summary Processor", true, errorMessage);
                throw;
            }
        }
        private async Task<List<OutletTaskModel>> GetOutletDataDict(long companyId, DateTime date, long employeeId)
        {
            var routesIds = await GetRouteIdsForDay(companyId, date, employeeId);
            return await _routeRepository.GetOutletsForRoutes(companyId, routesIds, date);
        }
        public async Task<List<long>> GetRouteIdsForDay(long companyId, DateTime today, long employeeId)
        {
            var data = await _routeRepository.GetRoutePlanData(companyId, today, employeeId);
            return data.Where(rp => rp.RouteId.HasValue && rp.DayNumber - 1 == (today - rp.StartDate).Days % GetRoutePlanDivisor((JourneyFrequency)rp.RepeatFrequency))
                .Select(rpi => rpi.RouteId.Value).ToList();
        }
        private static int GetRoutePlanDivisor(JourneyFrequency freq)
        {
            switch (freq)
            {
                case JourneyFrequency.FourWeeks:
                    return 28;
                case JourneyFrequency.TwoWeek:
                    return 14;
                case JourneyFrequency.OneWeek:
                    return 7;
                default:
                    return 365;
            }
        }
        private async Task<(Dictionary<long, List<TaskDetails>>, Dictionary<long, TaskDetails>)> GetTaskDataForOutlets(List<long> outletIds, long companyId)
        {
            var taskMaster = (await _taskManagementRepository.GetTaskForOutlets(companyId, outletIds));
            var outletWiseTaskMaster = taskMaster.GroupBy(s => s.OutletId).ToDictionary(s => s.Key, s => s.ToList());
            var taskWiseTaskMaster = taskMaster.GroupBy(s => s.TaskId).ToDictionary(s => s.Key, s => s.First());
            return (outletWiseTaskMaster, taskWiseTaskMaster);
        }
        private async Task<(Dictionary<long, List<TaskDetails>>, Dictionary<long, TaskDetails>)> GetTaskDataForOutOfRouteOutletVisits(List<long> taskIds, long companyId)
        {
            var taskMaster = await _taskManagementRepository.GetTaskForOutletsForTaskIds(companyId, taskIds, true);
            var outletWiseTaskMaster = taskMaster.GroupBy(s => s.OutletId).ToDictionary(s => s.Key, s => s.ToList());
            var taskWiseTaskMaster = taskMaster.GroupBy(s => s.TaskId).ToDictionary(s => s.Key, s => s.First());
            return (outletWiseTaskMaster, taskWiseTaskMaster);
        }
    }
}
