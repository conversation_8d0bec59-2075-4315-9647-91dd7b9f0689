# HCCBOrdersToUnifyProcessor

## Overview
HCCBOrdersToUnifyProcessor is a triggered processor that reads orders from the HccbOrders table and transforms them into ProductWiseDemandSales data for insertion into ClickHouse SecondaryDemand table.

## Features
- Reads orders from HccbOrders table since last sync time
- Tracks sync time using IntegrationLastSyncTimes table
- Handles lag by reading orders only up to UTCNOW - 1 minute
- Transforms data to ProductWiseDemandSales model
- Bulk inserts data into ClickHouse SecondaryDemand table
- Implements HCCBUnifyClickhouseSqlDataReader for ClickHouse operations

## Configuration
Update the following connection strings in `appsettings.json`:
- `HCCBDbConnectionString`: Connection to HCCB database
- `ClickHouseConnectionString`: Connection to ClickHouse database

## Data Transformation
The `TransformOrdersToProductWiseDemandSales` method in `Processor.cs` contains placeholder mapping logic. 
**User needs to implement the actual mapping from Hccb<PERSON>rder to ProductWiseDemandSales model.**

## Dependencies
- HCCB.DbStorage project (contains HCCBUnifyClickhouseSqlDataReader implementation)
- Core project (for ProcessorBase and logging)
- FA_Libraries (for various utilities)

## Usage
This is a console application that can be run manually or scheduled as a job.

## Notes
- The processor uses a 1-minute lag to handle any timing issues with order creation
- Integration ID "HCCBOrdersToUnify" is used for tracking sync times
- Default lookback period is 30 days for first run
- Supports multiple company IDs as configured in Dependencies.cs
