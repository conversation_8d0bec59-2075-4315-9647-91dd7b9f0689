using HCCB.Core.Models;
using HCCB.DbStorage.DbContexts;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class TaskManagementRepository : ITaskManagementRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;
        public TaskManagementRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
        }

        public async Task<List<TaskManagementTaskDto>> GetTasks(long companyId)
        {
            var focusAreaIds = await _readOnlyMasterDbContext.TaskManagementFocusArea.Where(a => a.CompanyId == companyId && a.IsDeactive != true).Select(b => b.Id).ToListAsync();
            return await _readOnlyMasterDbContext.TaskManagementTask
                .Where(w => w.CompanyId == companyId
                        && w.TaskManagementFocusAreaID.HasValue
                        && focusAreaIds.Contains(w.TaskManagementFocusAreaID.Value)
                        && !w.IsDeactive)
                .Select(s => new TaskManagementTaskDto
                {
                    TaskId = s.Id,
                    TaskManagementFocusAreaID = s.TaskManagementFocusAreaID,
                    TaskEntityType = s.TaskEntityType,
                    TaskEntityId = s.TaskEntityId,
                    TaskTarget = s.TaskTarget,
                    ProductHierarchyLevel = s.ProductHierarchyLevel,
                    ProductHierarchyIds = s.ProductHierarchyIds,
                    CalculationMeasure = s.CalculationMeasure,
                    RewardId = s.RewardId,
                    RewardQuantity = s.RewardQuantity,
                    CompanyId = s.CompanyId,
                    ProductSuggestedQtyList = s.ProductSuggestedQtyList
                })
                .ToListAsync();
        }

        public async Task<List<TaskDetails>> GetTaskForOutlets(long companyId, List<long> outletIds)
        {
            var outletData = await _readOnlyMasterDbContext.TaskManagementTask
                .Where(t => t.CompanyId == companyId && t.TaskEntityType == FlexibleTargetEntityType.Outlet && outletIds.Contains(t.TaskEntityId) && t.TaskManagementFocusAreaID.HasValue)
                .Select(t => new TaskDetails
                {
                    FocusAreaId = t.TaskManagementFocusAreaID.Value,
                    OutletId = t.TaskEntityId,
                    TaskId = t.Id,
                }).ToListAsync();

            var focusAreaIds = outletData.Select(t => t.FocusAreaId).Distinct().ToList();
            var focusAreaDict = await GetFocusAreaNames(companyId, focusAreaIds);
            var userFocusAreaDict = await GetFocusAreaDescriptions(companyId, focusAreaIds);

            return outletData.Select(item => new TaskDetails
            {
                OutletId = item.OutletId,
                TaskId = item.TaskId,
                FocusAreaId = item.FocusAreaId,
                FocusAreaName = focusAreaDict.TryGetValue(item.FocusAreaId, out string? value) ? value : null,
                FocusAreaDescription = userFocusAreaDict.TryGetValue(item.FocusAreaId, out string? description) ? description : null
            }).ToList();
        }

        public async Task<List<TaskDetails>> GetTaskForOutletsForTaskIds(long companyId, List<long> taskIds, bool includeDeactive = false)
        {
            var query = _readOnlyMasterDbContext.TaskManagementTask
                .Where(t => t.CompanyId == companyId && t.TaskEntityType == FlexibleTargetEntityType.Outlet && taskIds.Contains(t.Id) && t.TaskManagementFocusAreaID.HasValue);

            if (!includeDeactive)
            {
                query = query.Where(t => !t.IsDeactive);
            }
            var taskData = await query
                .Select(t => new
                {
                    FocusAreaId = t.TaskManagementFocusAreaID.Value,
                    t.TaskEntityId,
                    t.Id
                })
                .ToListAsync();

            var focusAreaIds = taskData.Select(t => t.FocusAreaId).Distinct().ToList();
            var focusAreaDescriptions = await GetFocusAreaDescriptions(companyId, focusAreaIds);
            var focusAreaNames = await GetFocusAreaNames(companyId, focusAreaIds);

            return taskData.Select(item => new TaskDetails
            {
                OutletId = item.TaskEntityId,
                TaskId = item.Id,
                FocusAreaId = item.FocusAreaId,
                FocusAreaName = focusAreaNames.TryGetValue(item.FocusAreaId, out string? value) ? value : null,
                FocusAreaDescription = focusAreaDescriptions.TryGetValue(item.FocusAreaId, out string? description) ? description : null
            }).ToList();
        }

        private async Task<Dictionary<long, string>> GetFocusAreaNames(long companyId, List<long> focusAreaIds)
        {
            var focusAreaData = await _readOnlyMasterDbContext.TaskManagementFocusArea
                .Where(fa => fa.CompanyId == companyId && !fa.IsDeactive.Value && focusAreaIds.Contains(fa.Id))
                .Select(fa => new { fa.Id, fa.Name })
                .ToListAsync();

            return focusAreaData.ToDictionary(fa => fa.Id, fa => fa.Name);
        }

        private async Task<Dictionary<long, string>> GetFocusAreaDescriptions(long companyId, List<long> focusAreaIds)
        {
            var userFocusAreaData = await _readOnlyMasterDbContext.TaskManagementUserFocusAreas
                .Where(fa => fa.CompanyId == companyId && !fa.IsDeactive.Value && fa.TaskManagementFocusAreaID.HasValue && focusAreaIds.Contains(fa.TaskManagementFocusAreaID.Value))
                .ToListAsync();

            return userFocusAreaData.GroupBy(fa => fa.TaskManagementFocusAreaID.Value).ToDictionary(g => g.Key, g => g.First().Description);
        }
    }
}
