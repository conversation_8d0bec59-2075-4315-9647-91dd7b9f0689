using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.Repositories.HccbRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using HCCB.Core.Helpers;
using HCCBTaskStatusProcessor.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Library.SlackService;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using Telegram.Bot;
using Core.Loggers;
using SlackNet;
using Library.Infrastructure.QueueService;

namespace HCCBTaskStatusProcessor.Configurations
{
    public static class Dependencies
    {
        public const string TaskSummaryQueue = "task-summary-queue";
        internal static long CompanyId = 193017;
        internal static string ChannelId = "C08D4PSBW4V";

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            CompanyId = configuration.GetValue<long>("CompanyId");
            ChannelId = configuration.GetValue<string>("SlackChannelId") ?? ChannelId;
            var storageconnectionString = configuration.GetConnectionString("HCCBStorageConnectionString");
            serviceProvider
            .AddDbContext<WritableHccbDataContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("WritableHccbDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider
            .AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("ReadOnlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider.AddScoped(e =>
               new TransactionDbSqlDataReader(configuration.GetConnectionString("ReadOnlyTransactionDbConnectionString"), 600));
            serviceProvider
            .AddDbContext<ReadOnlyTransactionDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("ReadOnlyTransactionDbConnectionString"), SqlResiliencyBuilder);
            });

            var slackstorageconnectionString = configuration.GetConnectionString("SlackStorageConnectionString");

            serviceProvider.AddScoped<HttpClientHandler>();

            serviceProvider.AddScoped<TaskStatusProcessor>();
            serviceProvider.AddScoped<HccbOrderService>();
            serviceProvider.AddScoped<TaskStatusService>();
            serviceProvider.AddScoped<TransactionService>();
            serviceProvider.AddScoped((s) => new QueueHandlerService(storageconnectionString));
            serviceProvider.AddScoped<IFaMasterRepository, FaMasterRepository>();

            serviceProvider.AddScoped<IHccbOrderRepository, HccbOrderRepository>();
            serviceProvider.AddScoped<ITaskManagementRepository, TaskManagementRepository>();
            serviceProvider.AddScoped<IHccbTaskRepository, HccbTaskRepository>();
            serviceProvider.AddScoped<ITransactionRepository, TransactionRepository>();

            serviceProvider.AddSingleton<ISlackApiClient>(provider =>
            {
                var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                return new SlackApiClient("xoxb-**********-7724326580263-FISHBycTTu9YUPWuy3nwBVlR");
            });
            serviceProvider.AddHttpClient();
            // Register SlackLogHelperV2
            serviceProvider.AddScoped<ISlackLogHelper, SlackLogHelperV2>(provider =>
            {
                var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                var slackClient = provider.GetRequiredService<ISlackApiClient>();
                var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");
                string channelId = ChannelId; // Use your Slack channel ID
                return new SlackLogHelperV2(httpClientFactory, slackClient, masterStorageConnectionString, channelId);
            });
            serviceProvider.AddScoped<FileLogger>();
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure(3,
                TimeSpan.FromSeconds(30),
                null);
        }

        public static class Configuration
        {
            public static IConfiguration GetConfiguration()
            {
                var configBuilder = new ConfigurationBuilder()
                    .AddJsonFile($"test.json", true, true)
                    .AddEnvironmentVariables();

                return configBuilder.Build();
            }
        }
    }
}
