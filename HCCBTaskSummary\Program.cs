using HCCBTaskSummary.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace HCCBTaskSummary
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
                .ConfigureAppConfiguration((hostingContext, configBuilder) =>
                {
                    configBuilder
                        .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
                        .AddEnvironmentVariables();
                })
                .ConfigureWebJobs(b =>
                {
                    b.AddAzureStorageQueues();
                })
                .ConfigureServices((context, services) =>
                {
                    Dependencies.SetUp(services, context.Configuration);
                })
                .UseConsoleLifetime();

            var host = builder.Build();
            using (host)
            {
                await host.RunAsync();
            }
        }
    }
}
