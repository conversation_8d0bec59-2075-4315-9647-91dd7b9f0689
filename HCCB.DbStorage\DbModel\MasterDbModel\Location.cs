﻿using Libraries.CommonEnums;
using System.ComponentModel.DataAnnotations.Schema;

namespace HCCB.DbStorage.DbModel.MasterDbModel
{
    [Table("F2KLocations")]
    public class Location
    {
        public long Id { get; set; }
        [Column("Company")]
        public long CompanyId { get; set; }
        public string ShopName { get; set; }
        public string GUID { get; set; }
        public string ErpId { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
        public string Address { get; set; }
        public string SubCity { get; set; }
        public string MarketName { set; get; }
        public string City { set; get; }
        public string State { set; get; }
        public long? ShopTypeId { set; get; }
        public bool IsFocused { set; get; }
        public bool IsKYC { set; get; }
        public OutletSegmentation Segmentation { set; get; }
        public long? BeatId { get; set; }
        public bool IsBlocked { set; get; }
        public string GSTIN { get; set; }
        public string FormattedAddress { get; set; }
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public string LocationTag { get; set; }
        public bool IsAssetPresent { set; get; }
        public string CustomTags { get; set; }
        public int? OutletCategory { get; set; }
        public int? OutletChannel { set; get; }
        public bool? AttributeBoolean1 { get; set; }
        public new DateTime CreatedAt { get; set; }
    }
    

    public class OutletTags
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
    }
}
