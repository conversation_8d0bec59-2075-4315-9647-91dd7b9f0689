using System.Diagnostics;
using System.Text.Json;
using Core.Loggers;
using HCCBTaskSummary.Configuration;
using HCCBTaskSummary.Models;
using HCCBTaskSummary.Services;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;

namespace HCCBTaskSummary
{
    public class TaskSummaryProcessor
    {
        private readonly ISlackLogHelper _slackLogHelper;
        private readonly IServiceProvider _serviceProvider;
        private const string TaskSummaryQueue = Dependencies.TaskSummaryQueue;

        public TaskSummaryProcessor(ISlackLogHelper slackLogHelper, IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _slackLogHelper = slackLogHelper;
        }

        public async Task ProcessTaskSummaryQueue([QueueTrigger(TaskSummaryQueue)] TaskSummaryQueueData queueData)
        {
            var sw = new Stopwatch();
            sw.Start();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    Console.WriteLine($"----------START----------");
                    Console.WriteLine($"Received Event...");
                    Console.WriteLine(JsonSerializer.Serialize(queueData));

                    var taskService = scope.ServiceProvider.GetRequiredService<TaskSummaryService>();

                    Console.WriteLine($"Processing task summary for EmployeeId: {queueData.EmployeeId} , Date: {queueData.Date:yyyy-MM-dd} , CompanyId: {queueData.CompanyId}");
                    await taskService.ProcessTaskSummary(queueData.EmployeeId, queueData.Date, queueData.CompanyId);

                    var timeTaken = sw.ElapsedMilliseconds;
                    Console.WriteLine($"✅ Event processed successfully in {timeTaken} milliseconds");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing queue message: {ex.Message}");
                await _slackLogHelper.SendCustomSlackMessage("Task Summary Processor", false, $"UnHandled Exception Occurred! \n",
                    exceptionMessage: ex.GetBaseException().Message);
                throw;
            }
            finally
            {
                sw.Stop();
                Console.WriteLine($"----------END----------");
            }
        }
    }
}
