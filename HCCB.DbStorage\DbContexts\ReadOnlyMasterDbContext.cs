﻿using Core.Models.MasterDbModels;
using HCCB.DbStorage.DbModel.MasterDbModel;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.DbContexts
{
    public class ReadOnlyMasterDbContext : DbContext
    {
        public ReadOnlyMasterDbContext(DbContextOptions<ReadOnlyMasterDbContext> options)
            : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableDetailedErrors();
        }

        public DbSet<Employee> Employees { get; set; }

        public DbSet<Distributor> Distributors { get; set; }
        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
        public DbSet<PositionCode> PositionCodes { get; set; }

        public DbSet<Location> Locations { get; set; }
        public DbSet<OutletTags> OutletTags { get; set; }

        public DbSet<Product> Products { get; set; }

        public DbSet<TaskManagementTask> TaskManagementTask { get; set; }

        public DbSet<TaskManagementFocusArea> TaskManagementFocusArea { get; set; }

        public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }
        public DbSet<EntityMarginSlab> EntityMarginSlabs { get; set; }
        public DbSet<RequestApprovalTimeline> RequestApprovalTimelines { get; set; }
        public DbSet<AssetDefinition> AssetDefinitions { get; set; }
        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }
        public DbSet<Scheme> Scheme { get; set; }
        public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }
    }
}
