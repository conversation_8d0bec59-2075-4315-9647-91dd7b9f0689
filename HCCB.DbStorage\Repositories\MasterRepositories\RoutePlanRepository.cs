﻿
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using HCCB_ADLS.DbStorage.MasterRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class RoutePlanRepository : IRoutePlanRepository
    {
        private readonly WritableMasterDbContext writableMasterDbContext;

        public RoutePlanRepository(WritableMasterDbContext writableMasterDbContext)
        {
            this.writableMasterDbContext = writableMasterDbContext;
        }
        public async Task UpdateRoutePlanEndDatesAsync(long companyId)
        {
            using var transaction = await writableMasterDbContext.Database.BeginTransactionAsync();

            try
            {
                var deletedEmployees = await writableMasterDbContext.Employees
                .Where(ce => ce.CompanyId == companyId && (ce.Deleted || ce.IsDeactive))
                .Select(ce => new { ce.Id, ce.LastUpdatedAt })
                .ToListAsync();

                var employeeLastUpdatedMap = deletedEmployees.ToDictionary(e => e.Id, e => e.LastUpdatedAt);
                var employeeIds = deletedEmployees.Select(e => e.Id).ToList();

                var routePlansToUpdate = new List<RoutePlan>();
                const int batchSize = 1000;

                // Split into batches
                for (int i = 0; i < employeeIds.Count; i += batchSize)
                {
                    var currentBatch = employeeIds.Skip(i).Take(batchSize).ToList();

                    var batchRoutePlans = await writableMasterDbContext.RoutePlans
                        .Where(rp =>
                            rp.CompanyId == companyId &&
                            !rp.IsDeactive &&
                            !rp.Deleted &&
                            rp.EndDate == null &&
                            currentBatch.Contains(rp.EmployeeId))
                        .ToListAsync();

                    routePlansToUpdate.AddRange(batchRoutePlans);
                }

                foreach (var routePlan in routePlansToUpdate)
                {
                    if (employeeLastUpdatedMap.TryGetValue(routePlan.EmployeeId, out var lastUpdatedAt))
                    {
                        routePlan.EndDate = lastUpdatedAt.AddMinutes(330);
                    }
                }

                await writableMasterDbContext.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

    }
}
