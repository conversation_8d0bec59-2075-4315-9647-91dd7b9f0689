using HCCBOrdersToUnifyProcessor;
using HCCBOrdersToUnifyProcessor.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

var builder = Host.CreateApplicationBuilder(args);

// Add configuration
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

// Setup dependencies
Dependencies.SetUp(builder.Services, builder.Configuration);

var host = builder.Build();

// Get the processor and run it
var processor = host.Services.GetRequiredService<Processor>();

try
{
    await processor.Process();
    Console.WriteLine("HCCB Orders To Unify Processor completed successfully.");
}
catch (Exception ex)
{
    Console.WriteLine($"Error running HCCB Orders To Unify Processor: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}
