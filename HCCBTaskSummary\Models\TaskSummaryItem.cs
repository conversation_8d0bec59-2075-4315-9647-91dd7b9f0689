﻿using Hccb.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HCCBTaskSummary.Models
{
    public class TaskSummaryItem
    {
        public long OutletId { get; set; }
        public long TaskId { get; set; }
        public long TaskFocusAreaId { get; set; }
        public TaskAchievementStatus TaskAchievementStatus { get; set; }
        public string? SearchText { get; set; }
        public int Segmentation { get; set; }
    }
}
