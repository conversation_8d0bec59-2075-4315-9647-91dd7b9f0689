using HCCB.DbStorage.DbModel.HccbDbModel;
using HCCB.DbStorage.DbContexts;
using Microsoft.EntityFrameworkCore;
using HCCB.Core.Models;
using Library.DateTimeHelpers;
using System.Data;

namespace HCCB.DbStorage.Repositories.HccbRepositories
{
    public class HccbTaskRepository : IHccbTaskRepository
    {
        private readonly WritableHccbDataContext _writableHccbDataContext;
        public HccbTaskRepository(WritableHccbDataContext writableHccbDataContext) 
        {
            _writableHccbDataContext = writableHccbDataContext;
        }

        public async Task SaveHccbTaskAchievementAndRewards(List<HccbTaskAchievementAndReward> achievementAndRewards)
        {
            const int batchSize = 2000;
            for (int i = 0; i < achievementAndRewards.Count; i += batchSize)
            {
                var batch = achievementAndRewards.Skip(i).Take(batchSize);
                await _writableHccbDataContext.HccbTaskAchievementAndRewards.AddRangeAsync(batch);
                await _writableHccbDataContext.SaveChangesAsync();
            }
        }

        public async Task DeleteHccbTaskAchievementAndRewards(long companyId, long dateKey)
        {
            var data = await _writableHccbDataContext.HccbTaskAchievementAndRewards.Where(w => w.CompanyId == companyId && w.DateKey == dateKey).ToListAsync();
            if (data.Count > 0)
            {
                _writableHccbDataContext.RemoveRange(data);
                await _writableHccbDataContext.SaveChangesAsync();
            }
        }
        public async Task<List<TaskModels>> GetTaskForEmployee(long companyId, long userId, DateTime date)
        {
            var taskLiveModels = await _writableHccbDataContext.HccbTaskAchievementAndRewards
                .Where(hccb => hccb.CompanyId == companyId
                      && hccb.EmployeeId == userId
                      && hccb.DateKey == date.GetDateKey())
                .Select(hccb => new TaskModels
                {
                    TaskEntityId = hccb.TaskEntityId,
                    TaskId = hccb.TaskId,
                    IsCompleted = hccb.IsCompleted,
                    DateKey = hccb.DateKey,
                    RouteId = hccb.RouteId
                }).ToListAsync();

            return taskLiveModels;
        }

        public async Task SaveTaskSummaryWithDetails(List<TaskSummary> taskSummaries)
        {
            var executionStrategy = _writableHccbDataContext.Database.CreateExecutionStrategy();

            await executionStrategy.ExecuteAsync(async () =>
            {
                using var transaction = await _writableHccbDataContext.Database.BeginTransactionAsync();
                try
                {
                    foreach (var taskSummary in taskSummaries)
                    {
                        var existingTaskSummary = await _writableHccbDataContext.TaskSummary
                            .Where(ts => ts.UserId == taskSummary.UserId && 
                                             ts.DateKey == taskSummary.DateKey &&
                                             ts.CompanyId == taskSummary.CompanyId &&
                                             ts.TaskFocusAreaId == taskSummary.TaskFocusAreaId).FirstOrDefaultAsync();

                        if (existingTaskSummary != null)
                        {
                            existingTaskSummary.TotalTasks = taskSummary.TotalTasks;
                            existingTaskSummary.AchievedTasks = taskSummary.AchievedTasks;
                            existingTaskSummary.MissedTasks = taskSummary.MissedTasks;
                            
                            var detailsToRemove = await _writableHccbDataContext.TaskSummaryDetails
                                .Where(d => d.TaskSummaryId == existingTaskSummary.Id)
                                .ToListAsync();
                            _writableHccbDataContext.RemoveRange(detailsToRemove);
                            
                            foreach (var detail in taskSummary.TaskSummaryDetails)
                            {
                                detail.TaskSummaryId = existingTaskSummary.Id;
                                _writableHccbDataContext.TaskSummaryDetails.Add(detail);
                            }
                        }
                        else
                        {
                            _writableHccbDataContext.TaskSummary.Add(taskSummary);
                        }
                    }
                    
                    await _writableHccbDataContext.SaveChangesAsync();
                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });
        }
    }
}
