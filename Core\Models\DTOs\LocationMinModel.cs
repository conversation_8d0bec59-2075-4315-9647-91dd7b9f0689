﻿namespace Core.Models.DTOs
{
    public class LocationMinModel
    {
        public long Id { get; set; }
        public bool IsFocused { set; get; }
        public int Segmentation { set; get; }
        public long BeatId { get; set; }
        public int? OutletChannel { set; get; }
        public int? OutletCategory { set; get; }
    }

    public class GeoHierarchy
    {
        public long BeatId { get; set; }
        public long TerritoryId { get; set; }
        public long RegionId { get; set; }
        public long ZoneId { get; set; }
        public long Level5GeoId { get; set; }
        public long Level6GeoId { get; set; }
        public long Level7GeoId { get; set; }
    }
}