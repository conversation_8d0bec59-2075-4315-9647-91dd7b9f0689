﻿using HCCBTasksManagementProcessor.Models;
using HCCBTasksManagementProcessor.Repositories;
using Libraries.CommonEnums;
using System.Text.Json;
using System.Linq;
using Library.StringHelpers;
using Core.Loggers;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace HCCBTasksManagementProcessor.Services;

public class TaskMasterService
{
    private readonly ITaskMasterRepository taskMasterRepository;
    private readonly IMastersRepository mastersRepository;
    private readonly ISlackLogHelper slackLogHelper;
    private readonly FileLogger fileLogger;
    private readonly List<string> recipientEmails = new List<string>
             {
                 "<EMAIL>",
                 "<EMAIL>"
             };
    private static readonly SemaphoreSlim semaphore = new SemaphoreSlim(10); // Limiting to 5 concurrent requests, adjust as needed
    public TaskMasterService(ITaskMasterRepository taskMasterRepository, IMastersRepository mastersRepository, ISlackLogHelper slackLogHelper, FileLogger fileLogger)
    {
        this.taskMasterRepository = taskMasterRepository;
        this.mastersRepository = mastersRepository;
        this.slackLogHelper = slackLogHelper;
        this.fileLogger = fileLogger;
    }

    public async Task ProcessTasks(List<HCCBTaskManagementProcessorData> tasks)
    {
        var sw = new Stopwatch();
        sw.Start();
        var companyId = tasks.First().CompanyId;
        var inputLocationErpIds = tasks.Where(t => !string.IsNullOrWhiteSpace(t.OutletErpId)).Select(t => t.OutletErpId).Distinct().ToList();
        var uniqueProductHierarchyIds = new HashSet<string>();
        foreach (var task in tasks)
        {
            if (!string.IsNullOrWhiteSpace(task.ProductSuggestedQtyList))
            {
                try
                {
                    // Parse the JSON string into a list of ProductSuggestedQtyList objects
                    var suggestedQtyList = JsonSerializer.Deserialize<List<ProductSuggestedQtyList>>(task.ProductSuggestedQtyList);

                    if (suggestedQtyList != null)
                    {
                        foreach (var item in suggestedQtyList)
                        {
                            uniqueProductHierarchyIds.Add(item.ProductHierarchyId);
                        }
                    }
                }
                catch (JsonException ex)
                {
                    fileLogger.WriteLine($"Failed to parse ProductSuggestedQtyList: {ex.Message}");
                }
            }
        }
        fileLogger.WriteLine($"Preprocessing took {sw.ElapsedMilliseconds} ms");
        sw.Restart();
        #region Get Master Data
        fileLogger.WriteLine($"Getting Master Data");
        // Convert the HashSet to a list of unique product hierarchy IDs
        var productErpIds = uniqueProductHierarchyIds.ToList();
        var dbLocations = await mastersRepository.GetLocations(companyId, inputLocationErpIds);
        var locationDict = dbLocations.ToDictionary(loc => loc.Id, loc => loc);
        var locationBeatDict = dbLocations.Where(l => l.BeatId != null).ToDictionary(l => l.Id, l => l.BeatId.Value);
        var beatList = locationBeatDict.Select(x => x.Value).Distinct().ToList();
        var distributorsForBeat = await mastersRepository.GetDistributorsForBeats(companyId, beatList);
        var distributorIds = distributorsForBeat.SelectMany(x => x.Value).Distinct().ToList();
        var productIdsInStocks = (await mastersRepository.GetStockforDistributors(companyId, distributorIds));
        var outletDict = dbLocations.GroupBy(x => x.ErpId.NormalizeCaps()).ToDictionary(x => x.Key, x => x.First().Id);
        var dbProducts = await mastersRepository.GetProducts(companyId, productErpIds);
        var productDict = dbProducts.GroupBy(x => x.ErpCode.NormalizeCaps()).ToDictionary(x => x.Key, x => x.First());
        var dbTaskFocusAreas = await mastersRepository.GetFocusArea(companyId);
        var taskFocusAreaErpDict = dbTaskFocusAreas.ToDictionary(x => x.ERPId.NormalizeCaps(), x => x);
        var taskFocusAreaDict = dbTaskFocusAreas.ToDictionary(x => x.Id, x => x);
        var dbRewards = await mastersRepository.GetFARewards(companyId);
        // filter dbTaskFocusAreas
        var numberTypeFocusAreaDict = dbTaskFocusAreas.Where(p => !string.IsNullOrEmpty(p.ERPId)).GroupBy(p => p.ERPId.NormalizeCaps()).ToDictionary(grp => grp.Key, grp => grp.FirstOrDefault()?.Id);
        fileLogger.WriteLine($"Getting Master Data took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Restart();
        #endregion Get Master Data

        #region TaskManagementTable

        #region Deactivate existing tasks of outlets

        fileLogger.WriteLine($"Deactivating existing tasks of outlets");

        // Create a list of long for the inputLocationErpIds using outletDict
        var hccbOutletIds = tasks.Where(t => !string.IsNullOrWhiteSpace(t.OutletErpId) && !t.IsFocusAreaHandledByFA && outletDict.ContainsKey(t.OutletErpId))
            .Select(t => outletDict[t.OutletErpId]).ToHashSet();
        var faHandledTasks = tasks
            .Where(t => !string.IsNullOrWhiteSpace(t.OutletErpId) && t.IsFocusAreaHandledByFA && outletDict.ContainsKey(t.OutletErpId))
            .Select(t => (
                TaskEntityId: outletDict[t.OutletErpId],
                TaskManagementFocusAreaId: taskFocusAreaErpDict[t.TaskManagementFocusAreaErpId].Id))
            .ToHashSet();

        List<TaskManagementTask> hccbTasksToDeactivate = new();
        List<long> hccbTasksToDeactivateIds = new();
        List<long> faTasksToDeactivateIds = new();

        if (hccbOutletIds.Any())
        {
            hccbTasksToDeactivate = (await mastersRepository.GetExistingTask(companyId))
                .Where(x => x.TaskEntityType == TaskEntityType.Outlet && x.TaskEntityId.HasValue && hccbOutletIds.Contains(x.TaskEntityId.Value))
                .ToList();

            hccbTasksToDeactivateIds = hccbTasksToDeactivate
                .Select(x => x.Id)
                .ToList();
        }

        if (faHandledTasks.Any())
        {
            faTasksToDeactivateIds = await mastersRepository.GetActiveTaskIdsByEntityAndFocusArea(companyId, faHandledTasks);
        }

        var tasksToDeactivateIds = hccbTasksToDeactivateIds
            .Union(faTasksToDeactivateIds)
            .ToList();
        // Deactivate tasks for the locations in tasksToDeactivate
        fileLogger.WriteLine($"Found {tasksToDeactivateIds.Count} tasks to deactivate");
        if (tasksToDeactivateIds.Count() > 0)
        {
            await taskMasterRepository.DeactivateTasks(tasksToDeactivateIds);
        }
        fileLogger.WriteLine($"Deactivating existing tasks of outlets took {((sw.ElapsedMilliseconds / 1000) / 60)} minutes");
        sw.Restart();
        #endregion Deactivate existing tasks of outlets

        #region Remove Outlet Tags for deactivated tasks

        fileLogger.WriteLine($"Removing Outlet Tags for deactivated tasks");
        var surveyTasks = hccbTasksToDeactivate
            .Where(x => x.TaskManagementFocusAreaId != null && taskFocusAreaDict[x.TaskManagementFocusAreaId.Value].Type == TaskManagementFocusAreaType.SurveyBased
                        && taskFocusAreaDict[x.TaskManagementFocusAreaId.Value].EntityId.HasValue)
            .ToList();

        var surveyIdList = surveyTasks
            .Select(t => taskFocusAreaDict[t.TaskManagementFocusAreaId.Value].EntityId.Value)
            .Distinct()
            .ToList();

        var surveysList = await mastersRepository.GetSurveyForIds(companyId, surveyIdList);

        var surveyDictionary = surveysList.ToDictionary(
            s => s.id,
            s => string.IsNullOrWhiteSpace(s.SurveyConstraints)
                ? new List<long>()
                : JsonSerializer.Deserialize<SurveyConstraintsDb>(s.SurveyConstraints)?.CustomTags ?? new List<long>()
        );

        var tasksGrouped = surveyTasks.GroupBy(x => x.TaskEntityId).ToList();

        var updateLocationList1 = new ConcurrentBag<Location>();

        await Parallel.ForEachAsync(tasksGrouped, async (item, cancellationToken) =>
        {
            await semaphore.WaitAsync();
            if (item.Key.HasValue)
            {
                if (locationDict.TryGetValue(item.Key.Value, out var location))
                {
                    // Collect old tags from surveyDict using HashSet to ensure uniqueness
                    var newOutletTags = new HashSet<long>();
                    foreach (var task in item)
                    {
                        if (surveyDictionary.TryGetValue(taskFocusAreaDict[task.TaskManagementFocusAreaId.Value].EntityId.Value, out var tags))
                        {
                            newOutletTags.UnionWith(tags); // Add tags to the set
                        }
                    }

                    // Deserialize existing tags
                    var oldOutletTags = string.IsNullOrWhiteSpace(location.CustomTags)
                        ? new HashSet<long>()
                        : JsonSerializer.Deserialize<HashSet<long>>(location.CustomTags) ?? new HashSet<long>();

                    // Remove old tags from existing using HashSet
                    oldOutletTags.ExceptWith(newOutletTags);

                    location.CustomTags = JsonSerializer.Serialize(oldOutletTags);
                    updateLocationList1.Add(location);
                }
            }
            semaphore.Release();
        });

        if (updateLocationList1.Count > 0)
        {
            await taskMasterRepository.InsertOutletTagDetails(updateLocationList1.ToList());
        }
        fileLogger.WriteLine($"Removing Outlet Tags for deactivated tasks took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Restart();
        #endregion Remove Outlet Tags for deactivated tasks


        #region  Create tasks

        #region Map Erp Id to Id

        var inputFocusAreas = tasks.Select(x => x.TaskManagementFocusAreaErpId.NormalizeCaps()).ToList();
        var inputRewards = tasks.Select(x => x.RewardType).ToList();

        #endregion Map Erp Id to Id
        fileLogger.WriteLine($"Creating Tasks");
        var groupedData = tasks
            .GroupBy(x => new { x.OutletErpId, x.TaskManagementFocusAreaErpId })
            .ToList();

        var tasksCreateList = new ConcurrentBag<TaskManagementTask>();

        await Parallel.ForEachAsync(groupedData, async (item1, cancellationToken) =>
        {
            await semaphore.WaitAsync();
            var item = item1.First();

            long? taskTarget = null;
            if (taskFocusAreaErpDict.TryGetValue(item.TaskManagementFocusAreaErpId.NormalizeCaps(), out var taskFocusArea))
            {
                if (taskFocusArea?.Type != null && (taskFocusArea.Type.Value == TaskManagementFocusAreaType.NumberBased || taskFocusArea.Type.Value == TaskManagementFocusAreaType.ProductTag))
                {
                    taskTarget = item.KPITarget ?? null;
                }

                #region ConvertProductErpIds

                var productIds = new List<long>();
                var skippedProductIds = new List<long>();
                var productSuggestedQty = new List<ProductSuggestedQty>();
                if (!string.IsNullOrEmpty(item.ProductSuggestedQtyList))
                {
                    var productList = JsonSerializer.Deserialize<List<ProductSuggestedQtyList>>(item.ProductSuggestedQtyList);
                    var itemProductErpIds = productList.Select(t => t.ProductHierarchyId).ToList();
                    foreach (var productErpId in itemProductErpIds)
                    {
                        productDict.TryGetValue(productErpId.NormalizeCaps(), out var product);
                        if (product != null)
                        {
                            productIds.Add(product.Id);
                        }
                    }

                    if (taskFocusArea?.Type != null && taskFocusArea.Type.Value == TaskManagementFocusAreaType.ProductTag)
                    {
                        // remove productIds which are not in stock - for task type 4
                        long beatId = 0;
                        if (!locationBeatDict.TryGetValue(outletDict.TryGetValue(item.OutletErpId, out var outletId) ? outletId : 0, out beatId))
                        {
                            fileLogger.WriteLine($"No Beat Found for Outlet: {item.OutletErpId}");
                        }
                        var distributors = new List<long>();
                        var productsInStock = new HashSet<long>();
                        if (!distributorsForBeat.TryGetValue(beatId, out distributors))
                        {
                            fileLogger.WriteLine($"No Distributor Found for Outlet: {item.OutletErpId}, Beat: {beatId}");
                        }
                        else
                        {
                            productsInStock = distributors.SelectMany(x => productIdsInStocks.TryGetValue(x, out var productList) ? productList : new List<long>()).ToHashSet();
                        }

                        foreach (var productItem in productList)
                        {
                            productDict.TryGetValue(productItem.ProductHierarchyId.NormalizeCaps(), out var product);
                            if (product != null && productsInStock.Contains(product.Id))
                            {
                                productSuggestedQty.Add(new ProductSuggestedQty { ProductHierarchyId = product.Id, SuggestedQty = productItem.SuggestedQty });
                            }
                        }
                        var validProductIds = new List<long>();

                        foreach (var id in productIds)
                        {
                            if (productsInStock.Contains(id))
                                validProductIds.Add(id);
                            else
                                skippedProductIds.Add(id);
                        }
                        productIds = validProductIds;
                        taskTarget = validProductIds.Count > 0 ? validProductIds.Count : 1;

                    }
                }

                #endregion ConvertProductErpIds
                tasksCreateList.Add(new TaskManagementTask
                {
                    TaskManagementFocusAreaId = taskFocusArea.Id,
                    TaskEntityType = TaskEntityType.Outlet,
                    TaskEntityId = outletDict.TryGetValue(item.OutletErpId, out var taskEntityId) ? taskEntityId : null,
                    TaskTarget = taskTarget,
                    Sequence = null,
                    ProductHierarchyLevel = item.ProductHierarchyLevel,
                    ProductHierarchyIds = productIds != null && productIds.Count > 0 ? JsonSerializer.Serialize(productIds) : null,
                    ProductSuggestedQtyList = taskFocusArea?.Type != null && taskFocusArea.Type.Value == TaskManagementFocusAreaType.ProductTag ? JsonSerializer.Serialize(productSuggestedQty) : null,
                    CalculationMeasure = item.KPIType,
                    Description = item.Description,
                    IsDeactive = false,
                    DeactivatedAt = null,
                    RewardId = dbRewards.FirstOrDefault(x => x.Type == item.RewardType)?.Id,
                    RewardQuantity = item.RewardQuantity ?? null,
                    CreationContext = "HccbTasksMasterProcessor",
                    CompanyId = item.CompanyId,
                    SkippedProductIds = skippedProductIds != null && skippedProductIds.Count > 0 ? JsonSerializer.Serialize(skippedProductIds) : null,
                    LastUpdatedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                });
            }
            else
            {
                fileLogger.WriteLine($"Task Focus Area not found for ERP ID: {item.TaskManagementFocusAreaErpId}");
            }
            semaphore.Release();
        });
        fileLogger.WriteLine($"Creating tasks. Received {tasksCreateList.Count} records.");
        if (tasksCreateList.Count > 0)
        {
            await taskMasterRepository.CreateTasks(tasksCreateList.ToList());
        }

        fileLogger.WriteLine($"Created tasks for Company ID: {companyId}.Received {tasksCreateList.Count} records.");
        fileLogger.WriteLine($"Creating Tasks took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Restart();
        #endregion Create tasks

        #endregion TaskManagementTable

        #region F2KLocationsTable

        #region Outlet Tag Changes
        fileLogger.WriteLine("Executing Outlet Tag Changes");
        // to do: remove invalid tasks(TaskManagementFocusAreaId, outleterp, any other field is inactive) before starting processing - after fetching master data
        var inputTasks = tasksCreateList.Select(x => (x.TaskManagementFocusAreaId, x.TaskEntityId)).ToList();
        var taskWithSurvey = inputTasks
            .Where(x => x.TaskManagementFocusAreaId != null && taskFocusAreaDict[x.TaskManagementFocusAreaId.Value].Type == TaskManagementFocusAreaType.SurveyBased
                        && taskFocusAreaDict[x.TaskManagementFocusAreaId.Value].EntityId.HasValue)
            .ToList();

        var surveyList = taskWithSurvey
            .Select(t => taskFocusAreaDict[t.TaskManagementFocusAreaId.Value].EntityId.Value)
            .Distinct()
            .ToList();

        var surveys = await mastersRepository.GetSurveyForIds(companyId, surveyList);

        var surveyDict = surveys.ToDictionary(
            s => s.id,
            s => string.IsNullOrWhiteSpace(s.SurveyConstraints)
                ? new List<long>()
                : JsonSerializer.Deserialize<SurveyConstraintsDb>(s.SurveyConstraints)?.CustomTags ?? new List<long>()
        );

        var tasksGroupedd = taskWithSurvey.GroupBy(x => x.TaskEntityId).ToList();

        var updateLocationList2 = new ConcurrentBag<Location>();
        await Parallel.ForEachAsync(tasksGroupedd, async (item, cancellationToken) =>
        {
            await semaphore.WaitAsync();
            if (item.Key.HasValue)
            {
                if (locationDict.TryGetValue(item.Key.Value, out var location))
                {
                    // Collect new tags from surveyDict using HashSet to ensure uniqueness
                    var newOutletTags = new HashSet<long>();
                    foreach (var task in item)
                    {
                        if (surveyDict.TryGetValue(taskFocusAreaDict[task.TaskManagementFocusAreaId.Value].EntityId.Value, out var tags))
                        {
                            newOutletTags.UnionWith(tags); // Add tags to the set
                        }
                    }

                    // Deserialize existing tags
                    var oldOutletTags = string.IsNullOrWhiteSpace(location.CustomTags)
                        ? new List<long>()
                        : JsonSerializer.Deserialize<List<long>>(location.CustomTags) ?? new List<long>();

                    // Merge old and new tags using HashSet
                    newOutletTags.UnionWith(oldOutletTags);

                    location.CustomTags = JsonSerializer.Serialize(newOutletTags);
                    updateLocationList2.Add(location);
                }
            }
            semaphore.Release();
        });

        if (updateLocationList2.Count > 0)
        {
            await taskMasterRepository.InsertOutletTagDetails(updateLocationList2.ToList());
        }
        fileLogger.WriteLine($"Outlet Tag Changes took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Restart();
        #endregion Outlet Tag Changes
        #endregion F2KLocationsTable

        #region RecommendedProductsTable

        fileLogger.WriteLine("Deleting RecommendedProducts");
        //filter created tasks
        // to do: check if tasksCreateList can be used
        var createdTasks = (await mastersRepository.GetExistingTask(companyId)).Where(x => x.TaskEntityType == TaskEntityType.Outlet && !x.IsDeactive);
        var uniqueCreatedTask = createdTasks.Select(x => (x.TaskManagementFocusAreaId, x.TaskEntityId)).ToHashSet();
        // Filter input tasks based on the created task combinations and extract TaskEntityId
        var validTaskEntityIds = inputTasks.Where(x => uniqueCreatedTask.Contains((x.TaskManagementFocusAreaId, x.TaskEntityId))).Select(x => x.TaskEntityId).ToHashSet();
        var dbRecommendedProducts = await taskMasterRepository.GetTag2RecommendedProducts(companyId);
        var distinctOutletErpWithSellFocus = tasks.Where(x => x.TaskManagementFocusAreaErpId.NormalizeCaps() == "TASK1005").GroupBy(x => x.OutletErpId)
            .Select(x => x.Key).ToList();
        var rowsToDelete = dbRecommendedProducts.Where(x => validTaskEntityIds.Contains(x.OutletId)).Select(x => x.Id).ToList();
        if (rowsToDelete.Count > 0)
        {
            try
            {
                await taskMasterRepository.DeleteRecommendedProducts(rowsToDelete);
            }
            catch (Exception ex)
            {
                fileLogger.WriteLine(ex.ToString());
                fileLogger.WriteLine($"Deactivation Failed for following ids: {rowsToDelete}");
                await slackLogHelper.SendFailureMessage(
                    jobMessage: "Failure: Recommended product deactivation -\r\n",
                    successStatus: false,
                    statusMessage: ex.Message,
                    failureMessage: "",
                    responseCode: 500,
                    responseMessage: "Deactivation Failed for following ids: " + rowsToDelete,
                    recipients: recipientEmails);
            }
        }
        fileLogger.WriteLine($"Deleting RecommendedProducts took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Restart();
        fileLogger.WriteLine("Creating RecommendedProducts");
        var recommendedProductsRowsToInsert = new ConcurrentBag<RecommendedProduct>();
        await Parallel.ForEachAsync(tasks, async (item, cancellationToken) =>
        {
            await semaphore.WaitAsync();
            if ((string.Equals(item.TaskManagementFocusAreaErpId, "TASK1005", StringComparison.OrdinalIgnoreCase) || string.Equals(item.TaskManagementFocusAreaErpId, "TASK1006", StringComparison.OrdinalIgnoreCase)) && !string.IsNullOrEmpty(item.ProductSuggestedQtyList)
                && (item.KPIType == TaskCalculationType.Quantity_Units || item.KPIType == TaskCalculationType.Quantity_StdUnits)
                && item.ProductHierarchyLevel == TasksProductHierarchy.SKU)
            {
                var suggestedQtyList = JsonSerializer.Deserialize<List<ProductSuggestedQtyList>>(item.ProductSuggestedQtyList) ?? new List<ProductSuggestedQtyList>();
                int rankCounter = 11;
                foreach (var product in suggestedQtyList)
                {
                    if (!productDict.ContainsKey(product.ProductHierarchyId.NormalizeCaps()))
                    {
                        fileLogger.WriteLine($"Product {product.ProductHierarchyId} not found for outlet {item.OutletErpId}");
                        continue;
                    }
                    if (!outletDict.ContainsKey(item.OutletErpId.NormalizeCaps()))
                    {
                        fileLogger.WriteLine($"Outlet {item.OutletErpId.NormalizeCaps()} not found");
                        continue;
                    }
                    int qty = 0;
                    if (item.KPIType == TaskCalculationType.Quantity_Units) { qty = product.SuggestedQty; }
                    if (item.KPIType == TaskCalculationType.Quantity_StdUnits)
                    {
                        qty = (int)Math.Round(product.SuggestedQty * productDict[product.ProductHierarchyId.NormalizeCaps()].StandardUnitConversionFactor);
                    }

                    recommendedProductsRowsToInsert.Add(new RecommendedProduct
                    {
                        Recommendation_Id = " ",
                        OutletId = outletDict[item.OutletErpId.NormalizeCaps()],
                        OutletErpId = item.OutletErpId,
                        CreationContext = "HCCBTasksManagementProcessor",
                        Tag = 2,
                        ProductId = productDict[product.ProductHierarchyId.NormalizeCaps()].Id,
                        ProductErpId = product.ProductHierarchyId,
                        SKU_CS = 0,
                        SKU_EA = 0,
                        PotentialQty = qty,
                        Rank = rankCounter,
                        CompanyId = companyId,
                        CreatedAt = DateTime.UtcNow,
                        LastUpdatedAt = DateTime.UtcNow
                    });
                    rankCounter += 2;
                }
            }
            semaphore.Release();
        });

        if (recommendedProductsRowsToInsert.Count > 0)
        {
            await taskMasterRepository.AddRecommendedProducts(recommendedProductsRowsToInsert.ToList());
        }

        #endregion RecommendedProductsTable

        #region  Update Is Processed
        fileLogger.WriteLine("Update Is Processed");
        var validTaskIds = tasks.Select(x => x.Id).ToList();
        await taskMasterRepository.UpdateProcessedTasks(validTaskIds);
        fileLogger.WriteLine($"Update Is Processed took  {((sw.ElapsedMilliseconds / 1000))} seconds");
        sw.Stop();
        #endregion Update Is Processed
    }


}
