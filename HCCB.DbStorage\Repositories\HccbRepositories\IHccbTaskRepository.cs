using HCCB.Core.Models;
using HCCB.DbStorage.DbModel.HccbDbModel;
using System.Collections.Generic;

namespace HCCB.DbStorage.Repositories.HccbRepositories
{
    public interface IHccbTaskRepository
    {
        Task SaveHccbTaskAchievementAndRewards(List<HccbTaskAchievementAndReward> achievementAndRewards);

        Task DeleteHccbTaskAchievementAndRewards(long companyId, long dateKey);
        Task<List<TaskModels>> GetTaskForEmployee(long companyId, long userId, DateTime date);
        Task SaveTaskSummaryWithDetails(List<TaskSummary> taskSummaries);
    }
}
