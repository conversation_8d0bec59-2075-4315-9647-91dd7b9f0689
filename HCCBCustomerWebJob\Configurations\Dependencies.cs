﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore;
using HCCBCustomerWebJob.Helpers;
using HCCBCustomerWebJob.DbContexts;
using HCCBOrderStatusWebJob.Services;
using HCCBCustomerWebJob.Services;
using HCCBCustomerMasterSalesAreaWebJob.Services;
using HCCBCustomerWebJob.Repositories;
using Core.APIHelpers;
using Core.Loggers;
using SlackNet.Extensions.DependencyInjection;
using Core.Helpers;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using HCCB.DbStorage.DbContexts;
using Library.Infrastructure.QueueService;
using HCCBAssetMappingProcessor.Services;
using System.Text.Json;
using HCCB.DbStorage.Repositories.MasterRepositories;
using HCCB.Core;
using HCCB.DbStorage.Repositories.HccbRepositories;
using Core.Repositories;
using HCCB.Core.Helpers;
using SlackNet;

namespace HCCBCustomerWebJob.Configurations
{
    public class Dependencies
    {
        #region IntegrationIds
        public const string CustomerMaster = "CustomerMaster";
        public const string CustomerMasterSalesArea = "CustomerMasterSalesArea";
        public const string AssetMaster = "AssetMaster";
        #endregion
        public static string ChannelId = "C08D4PSBW4V";
        public static string SlackWebHook = "*******************************************************************************";
        public const int ProdCompanyId = 193054;
        public static float Frequency = 0.5F; //in hours
        public static int OverlapInMinutes = 5; //in minutes
        public static string currentEnvironment = "local";

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            Console.WriteLine($"Setting up dependencies with Following Configurations:\n{JsonSerializer.Serialize(configuration.AsEnumerable(), new JsonSerializerOptions() { WriteIndented = true })}");
            ChannelId = configuration.GetValue<string>("Slack:ChannelId") ?? ChannelId;
            SlackWebHook = configuration.GetValue<string>("Slack:WebHookUrl") ?? SlackWebHook;
            Frequency = configuration.GetValue<float?>("FrequencyInHours") ?? Frequency;
            OverlapInMinutes = configuration.GetValue<int?>("OverlapInMinutes") ?? OverlapInMinutes;
            currentEnvironment = configuration.GetValue<string>("Environment");
            var configuredStartDate = configuration.GetValue<string>("hccbAPIhelper:StartDate");
            var configuredEndDate = configuration.GetValue<string>("hccbAPIhelper:EndDate");
            var connectionString = configuration.GetConnectionString("StorageConnectionString");
            var istNow = DateTime.UtcNow.AddMinutes(330);
            var startHour = istNow.Date.AddHours(istNow.Hour);
            var hCCBApiValues = new HCCBApiValues
            {
                userName = configuration.GetValue<string>("hccbAPIhelper:userName"),
                password = configuration.GetValue<string>("hccbAPIhelper:password"),
                masterInterfaceId = configuration.GetValue<int>("hccbAPIhelper:masterInterfaceId"),
                partnerId = configuration.GetValue<int>("hccbAPIhelper:partnerId"),
                salesAreainterfaceId = configuration.GetValue<int>("hccbAPIhelper:salesAreainterfaceId"),
                AssetsInterfaceId = configuration.GetValue<int>("hccbAPIhelper:AssetInterfaceID"),
                companyId = configuration.GetValue<long>("hccbAPIhelper:companyId"),
                HccbBaseUrl = configuration.GetValue<string>("hccbAPIhelper:HccbBaseUrl"),
                StartDate = string.IsNullOrWhiteSpace(configuredStartDate) ? startHour.AddHours(-Frequency).AddMinutes(-OverlapInMinutes) : DateTime.Parse(configuredStartDate).ToUniversalTime(),
                EndDate = string.IsNullOrWhiteSpace(configuredEndDate) ? startHour : DateTime.Parse(configuredEndDate).ToUniversalTime()
            };
            Console.WriteLine($"Running with Following HccbAPiValues:\n{JsonSerializer.Serialize(hCCBApiValues, new JsonSerializerOptions() { WriteIndented = true })}");
            var appConfigSettings = new AppConfigSettings
            {
                TelegramBotToken = configuration.GetValue<string>("Values:TelegramBotToken"),
                HCCBClientAPIToken = configuration.GetValue<string>("hccbAPIhelper:HCCBClientAPIToken"),
                APIBasePath = configuration.GetValue<string>("hccbAPIhelper:APIBasePath"),
                ExtApiBasePath = configuration.GetValue<string>("hccbAPIhelper:ExtApiBasePath")
            };
            serviceProvider
            .AddDbContext<HCCBFaDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("HCCBFaDataConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider
            .AddDbContextPool<WritableHccbDataContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("HCCBFaDataConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider.AddDbContextFactory<WritableHccbDataContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("HCCBFaDataConnectionString"), SqlResiliencyBuilder);
            });

            serviceProvider
            .AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("ReadOnlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider
            .AddDbContext<WritableMasterDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("WritableMasterDbConnectionString")));

            serviceProvider
            .AddDbContext<ReadOnlyTransactionDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("TransactionDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider.AddScoped(e =>
               new TransactionDbSqlDataReader(configuration.GetConnectionString("TransactionDbConnectionString"), 600));
            serviceProvider.AddTransient<RetryDelegatingHandler>();
            serviceProvider.AddHttpClient("ExternalApiClient", client =>
            {
                client.Timeout = TimeSpan.FromMinutes(10);
            }).AddHttpMessageHandler<RetryDelegatingHandler>();
            serviceProvider.AddSingleton((s) => new QueueHandlerService(connectionString));

            serviceProvider.AddScoped<CustomerMasterSalesAreaProcessor>();
            serviceProvider.AddScoped<AssetMappingsGetter>();
            serviceProvider.AddScoped<ITokenService, TokenService>();
            serviceProvider.AddScoped<IPageNumberService, PageNumberService>();
            serviceProvider.AddScoped<ICustomerMasterSalesAreaService, CustomerMasterSalesAreaService>();
            serviceProvider.AddScoped<IHccbIntegrationLogsRepository, HccbIntegrationLogsRepository>();
            serviceProvider.AddSingleton(d => hCCBApiValues);
            serviceProvider.AddScoped<IHCCBCustomerMasterSalesAreaService, HCCBCustomerMasterSalesAreaService>();
            serviceProvider.AddSingleton(e => appConfigSettings);

            //HccbServices
            serviceProvider.AddScoped<CustomerMasterProcessor>();
            serviceProvider.AddScoped<ICustomerMasterService, CustomerMasterService>();
            serviceProvider.AddScoped<IHCCBCustomerMasterService, HCCBCustomerMasterService>();
            serviceProvider.AddScoped<IFAEmployeeService, FAEmployeeService>();
            serviceProvider.AddScoped<IFAPositionService, FAPositionService>();
            serviceProvider.AddScoped<ICustomerMasterSalesAreaRepository, CustomerMasterSalesAreaRepository>();
            serviceProvider.AddScoped<IUserMasterRepository, UserMasterRepository>();
            serviceProvider.AddScoped<IRoutePlanRepository, RoutePlanRepository>();
            serviceProvider.AddScoped<APICallHelper>();
            serviceProvider.AddScoped<IHCCBUserMasterService, HCCBUserMasterService>();
            serviceProvider.AddScoped<IHCCBAcknowlegementService, HCCBAcknowlegementService>();
            serviceProvider.AddScoped<IHCCBGetAssetsService, HCCBGetAssetsService>();
            serviceProvider.AddScoped<CreateAssetEquipmentOutletMapping>();

            //Repositories
            serviceProvider.AddScoped<ITransactionRepository, TransactionRepository>();
            serviceProvider.AddScoped<IFaMasterRepository, FaMasterRepository>();
            serviceProvider.AddScoped<IAssetMasterRepository, AssetMasterRepository>();
            serviceProvider.AddScoped<ILastSyncTimeRepository, LastSyncTimeRepository>();

            //FAServices
            serviceProvider.AddScoped<IFAGeographyService, FAGeographyService>();
            serviceProvider.AddScoped<IFADistributorService, FADistributorService>();
            serviceProvider.AddScoped<IFAOutletMarginService, FAOutletMarginService>();
            serviceProvider.AddScoped<IFAOutletService, FAOutletService>();

            //Process
            serviceProvider.AddScoped<ProcessGeographies>();
            serviceProvider.AddScoped<ProcessDistributors>();
            serviceProvider.AddScoped<ProcessOutletMargins>();
            serviceProvider.AddScoped<ProcessDistributorBeatMappings>();
            serviceProvider.AddScoped<ProcessOutlets>();
            serviceProvider.AddScoped<ProcessPositions>();
            serviceProvider.AddScoped<ProcessEmployees>();
            serviceProvider.AddScoped<ProcessPositionBeatMappings>();
            serviceProvider.AddScoped<UserMasterProcessor>();
            serviceProvider.AddScoped<ProcessUserMaster>();
            serviceProvider.AddScoped<FileLogger>();
            serviceProvider.AddScoped<HttpRequestHelper>();
            serviceProvider.AddScoped<HttpRequestHelperV2>();
            serviceProvider.AddScoped<SqlQueryHelper>();
            serviceProvider.AddScoped<AssetMappingsProcessor>();
            serviceProvider.AddHttpClient().ConfigureHttpClientDefaults(d => d.ConfigureHttpClient(c => c.Timeout = TimeSpan.FromMinutes(10)));
            serviceProvider.AddSlackNet(c => c
                .UseApiToken("xoxb-**********-7724326580263-FISHBycTTu9YUPWuy3nwBVlR"));
            var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");

            serviceProvider.AddSingleton<ISlackLogHelper>(sp => new SlackLogHelperV2(sp.GetRequiredService<IHttpClientFactory>(),
                sp.GetRequiredService<ISlackApiClient>(), masterStorageConnectionString, ChannelId));
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.CommandTimeout(600);
            o.EnableRetryOnFailure(3,
                TimeSpan.FromSeconds(60),
                null);
        }

        internal static bool IsDailyRun()
        {
            return Frequency == 24;
        }
    }
}
