﻿using System.ComponentModel.DataAnnotations.Schema;

namespace HCCB.DbStorage.DbModel.MasterDbModel
{
    [Table("ClientEmployees")]
    public class Employee
    {
        public long Id { get; set; }

        [Column("Company")]
        public long CompanyId { get; set; }

        [Column("ClientSideId")]
        public string? ErpId { get; set; }
        [Column("IsDeleted")]
        public bool Deleted { get; set; }

        [Column("Deleted")]
        public bool IsDeactive { get; set; }

    }
}
