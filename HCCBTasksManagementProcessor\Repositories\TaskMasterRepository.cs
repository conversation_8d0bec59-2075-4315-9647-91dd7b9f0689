﻿using Microsoft.EntityFrameworkCore;
using HCCBTasksManagementProcessor.DbContexts;
using HCCBTasksManagementProcessor.Models;
using EFCore.BulkExtensions;

namespace HCCBTasksManagementProcessor.Repositories;

public class TaskMasterRepository : ITaskMasterRepository
{
    private readonly HCCBFaDbContext hccbDbContext;
    private readonly WritableMasterDbContext writableDb;

    public TaskMasterRepository(HCCBFaDbContext hccbDbContext, WritableMasterDbContext writableDb)
    {
        this.hccbDbContext = hccbDbContext;
        this.writableDb = writableDb;
    }

    public async Task<List<RecommendedProduct>> GetTag2RecommendedProducts(long companyId)
    {
        var recommendedProducts = await hccbDbContext.RecommendedProducts.Where(e => e.CompanyId == companyId && e.Tag == 2).ToListAsync();
        return recommendedProducts;
    }

    public async Task CreateTasks(List<TaskManagementTask> tasksCreateList)
    {
        const int batchSize = 5000;
        await writableDb.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
        {
            using var transaction = writableDb.Database.BeginTransaction();
            await writableDb.BulkInsertOrUpdateAsync(tasksCreateList, new BulkConfig
            {
                UseTempDB = true,
                BatchSize = batchSize
            });
            await transaction.CommitAsync();
        });
    }
    #region DeactivateTasks

    public async Task DeactivateTasks(List<long> tasksId)
    {
        const int batchSize = 2000;
        for (int i = 0; i < tasksId.Count; i += batchSize)
        {
            var batch = tasksId.Skip(i).Take(batchSize);
            await writableDb.TaskManagementTasks
                .Where(e => batch.Contains(e.Id))
                .ExecuteUpdateAsync(x => x
                .SetProperty(c => c.IsDeactive, true)
                .SetProperty(c => c.LastUpdatedAt, DateTime.UtcNow));
        }
    }
    #endregion DeactivateTasks

    public async Task InsertOutletTagDetails(List<Location> updateList)
    {
        const int batchSize = 5000;
        await writableDb.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
        {
            using var transaction = writableDb.Database.BeginTransaction();
            await writableDb.BulkUpdateAsync(updateList, new BulkConfig
            {
                UseTempDB = true,
                BatchSize = batchSize,
                PropertiesToIncludeOnUpdate = new List<string> { "CustomTags" }
            });
            await transaction.CommitAsync();
        });
    }

    #region DeleteRecommendedProducts

    public async Task DeleteRecommendedProducts(List<long> recommendedProducts)
    {
        const int batchSize = 5000;
        for (int i = 0; i < recommendedProducts.Count; i += batchSize)
        {
            var batch = recommendedProducts.Skip(i).Take(batchSize);
            var existingTasks = await hccbDbContext.RecommendedProducts
                .Where(e => batch.Contains(e.Id)).ExecuteDeleteAsync();
        }
    }

    #endregion DeleteRecommendedProducts

    #region AddRecommendedProducts

    public async Task AddRecommendedProducts(List<RecommendedProduct> recommendedProducts)
    {
        const int batchSize = 5000;
        await hccbDbContext.Database.CreateExecutionStrategy().ExecuteAsync(async () =>
        {
            using var transaction = hccbDbContext.Database.BeginTransaction();
            await hccbDbContext.BulkInsertAsync(recommendedProducts, new BulkConfig
            {
                UseTempDB = true,
                BatchSize = batchSize
            });
            await transaction.CommitAsync();
        });
    }

    #endregion AddRecommendedProducts

    #region MarkTask as Processed

    public async Task UpdateProcessedTasks(List<long> tasksIds)
    {
        const int batchSize = 5000;
        for (int i = 0; i < tasksIds.Count; i += batchSize)
        {
            var batch = tasksIds.Skip(i).Take(batchSize);
            await hccbDbContext.HCCBTaskManagementProcessorData
                .Where(e => batch.Contains(e.Id)).ExecuteUpdateAsync(t => t.SetProperty(d => d.IsProcessed, true));
        }
    }

    #endregion MarkTask as Processed
}
