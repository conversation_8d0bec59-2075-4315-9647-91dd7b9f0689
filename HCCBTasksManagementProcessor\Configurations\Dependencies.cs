using HCCBTasksManagementProcessor.Repositories;
using HCCBTasksManagementProcessor.DbContexts;
using HCCBTasksManagementProcessor.Helpers;
using HCCBTasksManagementProcessor.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http.Headers;
using Telegram.Bot;
using Core.Loggers;
using SlackNet;

namespace HCCBTasksManagementProcessor.Configurations
{
    public static class Dependencies
    {
        internal static string ChannelId = "";
        public static long CompanyId;
        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            var date = configuration.GetValue<string>("Values:Date");
            CompanyId = configuration.GetValue<long>("Values:CompanyId", 193017);
            ChannelId = configuration.GetValue<string>("Values:ChannelId", "C08D4PSBW4V");
            var appConfigSettings = new AppConfigSettings
            {
                TelegramBotToken = configuration.GetValue<string>("Values:TelegramBotToken"),
                Date = date != null ? DateTime.Parse(date) : null,
            };

            serviceProvider
            .AddDbContext<WritableMasterDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("WritableMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider
            .AddDbContext<MasterDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("ReadOnlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider
            .AddDbContext<HCCBFaDbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("HCCBFaDataConnectionString"), SqlResiliencyBuilder);
            });
            serviceProvider.AddScoped<TaskMasterProcessorService>();
            serviceProvider.AddScoped<TaskMasterService>();
            serviceProvider.AddScoped<ITaskMasterRepository, TaskMasterRepository>();
            serviceProvider.AddScoped<IMastersRepository, MastersRepository>();
            serviceProvider.AddScoped<HttpClientHandler>();
            serviceProvider.AddSingleton(e => appConfigSettings);

            //Telegram Bot
            serviceProvider.AddSingleton<ITelegramBotClient>(d => new TelegramBotClient(appConfigSettings.TelegramBotToken));
            serviceProvider.AddScoped<LogHelper>();
            serviceProvider.AddHttpClient();
            
            serviceProvider.AddSingleton<ISlackApiClient>(provider =>
            {
                var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                return new SlackApiClient("******************************************************");
            });
            serviceProvider.AddHttpClient();
            // Register SlackLogHelperV2
            serviceProvider.AddScoped<ISlackLogHelper, SlackLogHelperV2>(provider =>
            {
                var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                var slackClient = provider.GetRequiredService<ISlackApiClient>();
                var masterStorageConnectionString = configuration.GetConnectionString("MasterStorageConnectionString");
                return new SlackLogHelperV2(httpClientFactory, slackClient, masterStorageConnectionString, ChannelId);
            });
            serviceProvider.AddScoped<FileLogger>();

        }
        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.CommandTimeout(6000);
            o.EnableRetryOnFailure(3,
                TimeSpan.FromSeconds(30),
                null);
        }
    }
}
