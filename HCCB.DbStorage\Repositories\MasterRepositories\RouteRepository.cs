using Core.Models.MasterDbModels;
using HCCB.Core.Models;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.DbModel.MasterDbModel;
using Library.DateTimeHelpers;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class RouteRepository : IRouteRepository
    {
        private readonly ReadOnlyMasterDbContext _readOnlyMasterDbContext;
        private readonly MasterDbSqlDataReader _masterDbSqlDataReader;

        public RouteRepository(ReadOnlyMasterDbContext readOnlyMasterDbContext, MasterDbSqlDataReader masterDbSqlDataReader)
        {
            _readOnlyMasterDbContext = readOnlyMasterDbContext;
            _masterDbSqlDataReader = masterDbSqlDataReader;
        }

        public async Task<List<EmployeeRoutePlans>> GetRoutePlanData(long companyId, DateTime today, long employeeId)
        {
            var sql = $@"Select rpi.RouteId,
                                                rpi.DayNumber,rp.RepeatFrequency
                                                from RoutePlanItems rpi
                                                join RoutePlans rp on rpi.RoutePlanId = rp.Id
                                                where (rp.EndDate> '{today.ToString("yyyy-MM-dd HH:mm:ss")}' or rp.EndDate is null) and rp.EffectiveDate <= '{today.ToString("yyyy-MM-dd HH:mm:ss")}'
                                                and rp.IsDeactive= 0 and rp.Deleted = 0 and rp.CompanyId={companyId} and rp.EmployeeId = {employeeId}
                                                group by rpi.RouteId,rpi.Id,rp.EmployeeId,rpi.ReasonCategory,rpi.DayNumber,rp.RepeatFrequency,rpi.Reason,rp.StartDate ";
            return (await _masterDbSqlDataReader.GetModelFromQueryAsync<EmployeeRoutePlans>(sql)).ToList();
        }

        public async Task<List<OutletTaskModel>> GetOutletsForRoutes(long companyId, List<long> routeIds, DateTime date)
        {
            var data = await _readOnlyMasterDbContext.RouteOutletMappings
                .Where(s => s.CompanyId == companyId && routeIds.Contains(s.RouteId) && !s.Deleted &&
                            s.Location.CreatedAt <= date).Select(s => new OutletTaskModel
                            {
                                OutletId = s.Location.Id,
                                OutletName = s.Location.ShopName,
                                RouteId = s.RouteId,
                                QualifiedDate = date.GetDateKey()
                            }).ToListAsync();
            return data;
        }
    }
}
