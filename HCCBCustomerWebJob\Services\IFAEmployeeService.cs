﻿using HCCBCustomerWebJob.Models;
using Library.ResponseHelpers;

namespace HCCBCustomerWebJob.Services
{
    public interface IFAEmployeeService
    {
        Task<List<ApiResponseMessage>> CreateUpdateEmployee(List<CreateEmployee> position, long integrationId = 0);
        Task<List<EmployeeDetails>> GetAllEmployee();
        Task<List<ActivateDeactivateUserResponse>> DeactivateEmployees(List<string> erpIds, long integrationId = 0);
        Task<List<ActivateDeactivateUserResponse>> ReactivateEmployees(List<EmployeePositionDetails> employeeDetails, long integrationId = 0);
    }
}
