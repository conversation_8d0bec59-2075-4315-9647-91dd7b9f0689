﻿using HCCB.Core.Models;
using HCCB.DbStorage.DbModel.HccbDbModel;
using HCCB.DbStorage.DbModel.TransactionDbModel;

namespace HCCB.DbStorage.Repositories.TransactionRepositories
{
    public interface ITransactionRepository
    {
        Task<List<long>> GetDateKeysForNewData(long companyId, DateTime startTime, DateTime endTime);
        Task<List<HccbTaskAchievementAndReward>> GetTaskAchievementAndRewards(long companyId, long dateKey);
        Task<List<OutletCreationRequestData>> GetOutletCreationRequests(long companyId, DateTime startTime, DateTime endTime);
        Task<AssetAllocation> GetAssetAllocationRequestById(long id, long companyId);
        Task<AssetReallocationRequest> GetAssetRellocationRequestById(long id, long companyId);
    }
}
