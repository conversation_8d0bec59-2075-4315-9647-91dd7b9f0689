{
  "ConnectionStrings": {
    "HCCBDbConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;",
    "ClickHouseConnectionString": "Host=unify-clickhouse-hccb.fieldassist.io;Protocol=http;Port=8123;Username=admin;Password=2fw94RmdT3276dfgrtcrctyXZPavHcx8;Database=unify",
    "ReadonlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3_HCCB;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;",
  },
  "CompanyId": 193017,
  "ReportingApi": {
    "BaseUrl": "https://fa-reportapi.fieldassist.io/",
    "Token": "56SbkbmV+#?p+dSNgGPNz8"
  },
  "Slack": {
    "ChannelId": "C08D4PSBW4V"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
