﻿using Core.Models.MasterDbModels;
using HCCB_ADLS.DbStorage.MasterRepositories.Models;
using Microsoft.EntityFrameworkCore;

namespace HCCB.DbStorage.DbContexts
{
    public class WritableMasterDbContext : DbContext
    {
        public WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.EnableDetailedErrors();
        }

        public DbSet<RequestApprovalTimeline> RequestApprovalTimelines { get; set; }
        public DbSet<ClientEmployee> Employees { get; set; }
        public DbSet<RoutePlan> RoutePlans { get; set; }
    }
}
