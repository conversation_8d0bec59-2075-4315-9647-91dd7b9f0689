﻿using HCCBCustomerWebJob.Helpers;
using HCCBCustomerWebJob.Models;
using Core.APIHelpers;
using Library.ResponseHelpers;
using Core.Loggers;
using System.Net;
using System.Collections.Concurrent;
using HCCB.Core.Helpers;
using Core.Models.HccbDbModels;
using System.Text.Json;

namespace HCCBCustomerWebJob.Services
{
    public class FAEmployeeService : IFAEmployeeService
    {
        private readonly AppConfigSettings appConfigSettings;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly HttpRequestHelperV2 _httpRequestHelper;
        private readonly FileLogger _fileLogger;
        private readonly HCCBApiValues hCCBApiValues;
        public FAEmployeeService(AppConfigSettings appConfigSettings, IHttpClientFactory httpClientFactory, HttpRequestHelperV2 httpRequestHelper, FileLogger fileLogger, HCCBApiValues hCCBApiValues)
        {
            this.appConfigSettings = appConfigSettings;
            _httpClientFactory = httpClientFactory;
            _httpRequestHelper = httpRequestHelper;
            _fileLogger = fileLogger;
            this.hCCBApiValues = hCCBApiValues;
        }

        public async Task<List<ApiResponseMessage>> CreateUpdateEmployee(List<CreateEmployee> employees, long integrationId = 0)
        {
            string apiUrl = $"{appConfigSettings.ExtApiBasePath}/api/v3/Employee/AddHierarchyV2";
            string authorizationHeader = $"Basic {appConfigSettings.HCCBClientAPIToken}";

            ConcurrentBag<ApiResponseMessage> finalApiResponse = new();
            var apiTasks = new List<Task>();

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

            for (int i = 0; i < employees.Count; i++)
            {
                int index = i;

                if (apiTasks.Count >= 5)
                {
                    await Task.WhenAll(apiTasks);
                    apiTasks.Clear();
                }

                apiTasks.Add(Task.Run(async () =>
                {
                    var erps = new List<string> { employees[index].ErpId };

                    var hccbIntegrationLogs = new List<HCCBIntegrationAPILog>
                    {
                        new HCCBIntegrationAPILog
                        {
                            CompanyId = hCCBApiValues.companyId,
                            EntityEnum = EntityEnum.Employees,
                            APIName = apiUrl,
                            HCCBIntegrationLogID = integrationId,
                            InputErpIds = JsonSerializer.Serialize(erps),
                            ApiType = ApiType.NsExtApi
                        }
                    };
                    var apiResponse = await _httpRequestHelper.GetParsedResponse<ApiResponseMessage>(
                        httpClient, apiUrl, employees[index],
                        allowedFailureStatusCodes: new List<HttpStatusCode> { HttpStatusCode.BadRequest },
                        doNotThrowError: true,
                        hCCBIntegrationAPILogs: hccbIntegrationLogs
                    );

                    if (apiResponse != null)
                    {
                        finalApiResponse.Add(apiResponse);
                    }
                }));
            }

            if (apiTasks.Count > 0)
            {
                await Task.WhenAll(apiTasks);
            }

            return finalApiResponse.ToList();
        }

        public async Task<List<EmployeeDetails>> GetAllEmployee()
        {
            string apiUrl = $"{appConfigSettings.ExtApiBasePath}/api/masterdata/employee/list?EpochTime=0";
            string authorizationHeader = $"Basic {appConfigSettings.HCCBClientAPIToken}";

            using var httpClient = _httpClientFactory.CreateClient();

            var employees = await _httpRequestHelper.GetParsedGetResponse<List<EmployeeDetails>>(
                httpClient, apiUrl, authorizationHeader,
                allowedFailureStatusCodes: new List<HttpStatusCode> { HttpStatusCode.BadRequest },
                doNotThrowError: true
            );

            return employees ?? new List<EmployeeDetails>();
        }
        public async Task<List<ActivateDeactivateUserResponse>> DeactivateEmployees(List<string> erpIds, long integrationId = 0)
        {
            string apiUrl = $"{appConfigSettings.ExtApiBasePath}/api/V3/Employee/DeactivateFieldUser";
            string authorizationHeader = $"Basic {appConfigSettings.HCCBClientAPIToken}";

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

            ConcurrentBag<ActivateDeactivateUserResponse> responsesBag = new();
            var apiTasks = new List<Task>();
            int batchSize = 1000;

            var batches = erpIds
                .Select((erp, index) => new { erp, index })
                .GroupBy(x => x.index / batchSize)
                .Select(g => g.Select(x => x.erp).ToList())
                .ToList();

            foreach (var batch in batches)
            {
                if (apiTasks.Count >= 5)
                {
                    await Task.WhenAll(apiTasks);
                    apiTasks.Clear();
                }

                apiTasks.Add(Task.Run(async () =>
                {
                    var hccbIntegrationLogs = new List<HCCBIntegrationAPILog>
                    {
                        new HCCBIntegrationAPILog
                        {
                            CompanyId = hCCBApiValues.companyId,
                            EntityEnum = EntityEnum.UserDeactivation,
                            APIName = apiUrl,
                            HCCBIntegrationLogID = integrationId,
                            InputErpIds = JsonSerializer.Serialize(batch),
                            ApiType = ApiType.NsExtApi
                        }
                    };
                    var apiResponse = await _httpRequestHelper.GetParsedResponse<ApiResponseV2<ClientEmployeeEntities>>(
                        httpClient,
                        apiUrl,
                        batch,
                        allowedFailureStatusCodes: new List<HttpStatusCode> { HttpStatusCode.BadRequest },
                        doNotThrowError: true,
                        hCCBIntegrationAPILogs: hccbIntegrationLogs
                    );

                    if (apiResponse?.ResponseList != null && apiResponse.ResponseList.Any())
                    {
                        foreach (var item in apiResponse.ResponseList.Where(r => r.Entities != null))
                        {
                            responsesBag.Add(new ActivateDeactivateUserResponse
                            {
                                ERPId = item.Entities.ErpId,
                                Message = item.Message ?? (item.Success ? "Deactivation successful" : "Deactivation failed"),
                                ResponseStatus = item.Success ? ResponseStatus.Success : ResponseStatus.Failure
                            });
                        }
                    }
                    else
                    {
                        var globalStatus = apiResponse?.IsSuccess() == true ? ResponseStatus.Success : apiResponse?.ResponseStatus ?? ResponseStatus.Failure;
                        var message = apiResponse?.Message ?? (globalStatus == ResponseStatus.Success ? "Deactivation successful" : "Deactivation failed");

                        foreach (var erpId in batch)
                        {
                            responsesBag.Add(new ActivateDeactivateUserResponse
                            {
                                ERPId = erpId,
                                ResponseStatus = globalStatus,
                                Message = message
                            });
                        }
                    }
                }));
            }

            if (apiTasks.Count > 0)
            {
                await Task.WhenAll(apiTasks);
            }

            return responsesBag.ToList();
        }

        public async Task<List<ActivateDeactivateUserResponse>> ReactivateEmployees(List<EmployeePositionDetails> employeeDetails, long integrationId = 0)
        {
            const int batchSize = 1000;
            var allResponses = new List<ActivateDeactivateUserResponse>();

            for (int i = 0; i < employeeDetails.Count; i += batchSize)
            {
                var currentBatch = employeeDetails.Skip(i).Take(batchSize).ToList();
                string apiUrl = $"{appConfigSettings.ExtApiBasePath}/api/V3/Employee/ReactivateFieldUser";
                string authorizationHeader = $"Basic {appConfigSettings.HCCBClientAPIToken}";

                using var httpClient = _httpClientFactory.CreateClient();

                var inputErpIdsJson = JsonSerializer.Serialize(currentBatch.Select(e => e.UserErpId));

                var hccbIntegrationLogs = new List<HCCBIntegrationAPILog>
                {
                    new HCCBIntegrationAPILog
                    {
                        CompanyId = hCCBApiValues.companyId,
                        EntityEnum = EntityEnum.UserReactivation,
                        APIName = apiUrl,
                        HCCBIntegrationLogID = integrationId,
                        InputErpIds = inputErpIdsJson,
                        ApiType = ApiType.NsExtApi
                    }
                };

                var apiResponse = await _httpRequestHelper.GetParsedResponse<ApiResponseV2<ClientEmployeeEntities>>(
                    httpClient, apiUrl, authorizationHeader, currentBatch,
                    allowedFailureStatusCodes: new List<HttpStatusCode> { HttpStatusCode.BadRequest },
                    doNotThrowError: true,
                    hCCBIntegrationAPILogs: hccbIntegrationLogs
                );

                var batchResponses = apiResponse?.ResponseList?.Select(r => new ActivateDeactivateUserResponse
                {
                    ERPId = r.Entities.ErpId,
                    Message = r.Message,
                    ResponseStatus = r.Success ? ResponseStatus.Success : ResponseStatus.Failure,
                }).ToList() ?? new List<ActivateDeactivateUserResponse>();

                allResponses.AddRange(batchResponses);
            }

            return allResponses;
        }
    }
}
