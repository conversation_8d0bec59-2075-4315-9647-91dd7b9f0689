using Core.Loggers;
using Microsoft.EntityFrameworkCore;
using Core.Abstracts;
using HCCB.DbStorage.DbContexts;
using HCCBOrdersToUnifyProcessor.Configuration;
using Libraries.CommonEnums.HCCB;
using HCCB.DbStorage.DbModel.HccbDbModel;
using Core.Repositories;
using HCCB.DbStorage.DbModel.HccbUnifyDbModel;
using Core.Services;
using Core.Models;
using Library.DateTimeHelpers;
using Core.Models.DTOs;
using NetTopologySuite.IO;

namespace HCCBOrdersToUnifyProcessor
{
    public class Processor(ISlackLogHelper slackLogHelper,
        FileLogger fileLogger, 
        WritableHccbDataContext hccbDataContext,
        HCCBUnifyClickhouseSqlDataReader clickHouseDataReader,
        ILastSyncTimeRepository lastSyncTimeRepository,
        MTDService mTDService,
        IPositionCodeRepository positionCodeRepository,
        ILocationsRepository locationsRepository,
        IProductsRepository productsRepository)
        : ProcessorBase(fileLog<PERSON>, slackLogHelper)
    {
        private readonly WritableHccbDataContext _hccbDataContext = hccbDataContext;
        private readonly HCCBUnifyClickhouseSqlDataReader _clickHouseDataReader = clickHouseDataReader;
        private readonly ILastSyncTimeRepository lastSyncTimeRepository = lastSyncTimeRepository;
        private readonly MTDService mTDService = mTDService;
        private readonly IPositionCodeRepository positionCodeRepository = positionCodeRepository;
        private readonly ILocationsRepository locationsRepository = locationsRepository;
        private readonly IProductsRepository productsRepository = productsRepository;
        private const string ProcessorName = "HCCB Orders To Unify Processor";
        private const string IntegrationId = "HCCBOrdersToUnify";
        private const int batchSize = 100000;
        private static string ChannelId = Dependencies.ChannelId;
        private static List<HccbOrderSource> hccbOrderSources = new List<HccbOrderSource>
        {
            HccbOrderSource.TellSell,
            HccbOrderSource.CokeBuddy
        };

        protected override string LogHeader
        {
            get; set;
        }

        protected override string GetChannelId()
        {
            return ChannelId;
        }

        protected override string GetProcessorName()
        {
            return ProcessorName;
        }

        protected override void _ValidateArguments(params object?[] args)
        {
            return;
        }

        protected override async Task _Process(params object?[] args)
        {
            await ProcessCompanyOrders(Dependencies.CompanyId);
        }

        private async Task ProcessCompanyOrders(long companyId)
        {
            try
            {
                await _slackLogHelper.SendMessageToSlack($"[{companyId}]: Starting HCCB Orders to Unify processing");
                
                // Get last sync time
                var lastSyncTime = (await lastSyncTimeRepository.GetLastSyncTimeForIntegration(companyId, IntegrationId))?.LastSyncTime ?? DateTime.UtcNow.AddDays(-90);
                
                // Calculate end time (UTCNOW - 1 minute to handle lag)
                var endTime = DateTime.UtcNow.AddMinutes(-10);
                
                await _slackLogHelper.SendMessageToSlack($"[{companyId}]: Processing orders from {lastSyncTime} to {endTime}");
                
                var userHierarchies = (await positionCodeRepository.GetPositionUserHierarchies(companyId)).ToLookup(h => h.UserId, h => h);

                var productHierarchies = (await productsRepository.GetProductHierarchy(companyId)).ToDictionary(p => p.ProductId, p => p);

                // Read orders from HccbOrders table
                var ordersQueryable = _hccbDataContext.HccbOrders
                    .Where(o => o.CompanyId == companyId
                               && hccbOrderSources.Contains(o.OrderSourceEnum)
                               && o.CreatedAt > lastSyncTime
                               && o.CreatedAt <= endTime).OrderBy(o => o.Id);

                int processedRecordsCount = 0;
                var newLastSyncTime = lastSyncTime;
                int recordsToProcess = await ordersQueryable.CountAsync();
                await _slackLogHelper.SendMessageToSlack($"[{companyId}]: Found {recordsToProcess} records to process.");

                try
                {
                    do
                    {
                        var orders = await ordersQueryable.Skip(processedRecordsCount).Take(batchSize).AsNoTracking().ToListAsync();
                        await FlushRecordsToClickHouse(companyId, orders, userHierarchies, productHierarchies);
                        processedRecordsCount += orders.Count;
                        var newSyncTimeCandidate = orders.Max(o => o.CreatedAt);
                        newLastSyncTime = newSyncTimeCandidate > newLastSyncTime ? newSyncTimeCandidate : newLastSyncTime;
                    }
                    while (processedRecordsCount < recordsToProcess);
                }
                finally
                {
                    // Update last sync time
                    await lastSyncTimeRepository.UpsertLastSyncTimeForIntegration(companyId, IntegrationId, newLastSyncTime);
                }
                
                await _slackLogHelper.SendMessageToSlack($"[{companyId}]: Successfully inserted {processedRecordsCount} records into ClickHouse");
                
            }
            catch (Exception ex)
            {
                await _slackLogHelper.SendMessageToSlack($"[{companyId}]: Error processing orders: {ex.Message}");
                throw;
            }
        }

        private async Task FlushRecordsToClickHouse(long companyId, List<HccbOrder> orders, ILookup<long, PositionUserHierarchy> userHierarchies, 
            Dictionary<long, ProductFlat> productHierarchies)
        {
            var distinctOrderDates = orders.Select(order => order.DocumentDate.Date).Distinct().ToList();

            var locationIds = orders.Select(order => order.F2kLocations_Id).Distinct().ToList();

            var locationMins = (await locationsRepository.GetLocationMinModels(companyId, locationIds)).ToDictionary(l => l.Id, l => l);

            var beatIds = locationMins.Values.Select(l => l.BeatId).Distinct().ToList();

            var locationHierarchy = (await locationsRepository.GetGeoHierarchy(companyId, beatIds)).ToDictionary(b => b.BeatId, b => b);

            var distinctDatesDict = new Dictionary<DateTime, FA_MTD_LMTD>();

            foreach (var date in distinctOrderDates)
            {
                distinctDatesDict.Add(date, await mTDService.GetDates(companyId, date, Dependencies.YearStartMonth, includeToday: true));
            }

            // Transform orders to ProductWiseDemandSales model
            var secondaryDemandData = orders.Select(order => {
                locationMins.TryGetValue(order.F2kLocations_Id, out var locationMin);
                return new SecondaryDemand(order,
                    distinctDatesDict[order.DocumentDate.Date],
                    userHierarchies[order.ClientEmployee_Id].FirstOrDefault() ?? new PositionUserHierarchy(),
                    locationMin ?? new LocationMinModel(),
                    locationHierarchy.TryGetValue(locationMin?.BeatId ?? 0, out var geoHierarchy) ? geoHierarchy : new GeoHierarchy(),
                    productHierarchies.TryGetValue(order.FaCompanyProducts_Id, out var product) ? product : new ProductFlat()
                    );
                }).ToList();

            // Insert into ClickHouse SecondaryDemand table
            var insertedCount = await _clickHouseDataReader.BulkCopyData(secondaryDemandData, "SecondaryDemand");
        }
    }
}
