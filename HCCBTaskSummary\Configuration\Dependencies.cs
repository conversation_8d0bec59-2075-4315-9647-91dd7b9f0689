using Core.Loggers;
using HCCB.Core.Helpers;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.Repositories.HccbRepositories;
using HCCB.DbStorage.Repositories.MasterRepositories;
using HCCB.DbStorage.Repositories.TransactionRepositories;
using HCCBTaskSummary.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SlackNet;
using SlackNet.Extensions.DependencyInjection;

namespace HCCBTaskSummary.Configuration
{
    public static class Dependencies
    {
        public static string ChannelId = "C08D4PSBW4V";
        public const string TaskSummaryQueue = "task-summary-queue";

        public static void SetUp(IServiceCollection services, IConfiguration config)
        {
            ChannelId = config.GetValue<string>("Slack:ChannelId") ?? ChannelId;
            
            services.AddDbContext<ReadOnlyMasterDbContext>(options =>
            {
                options.UseSqlServer(config.GetConnectionString("ReadonlyMasterDbConnectionString"), SqlResiliencyBuilder);
            });
            services
            .AddDbContext<WritableHccbDataContext>(options =>
            {
                options.UseSqlServer(config.GetConnectionString("WritableHccbDbConnectionString"), SqlResiliencyBuilder);
            });
            services.AddScoped(e =>
               new MasterDbSqlDataReader(config.GetConnectionString("ReadonlyMasterDbConnectionString"), 600));
            services.AddScoped(e =>
               new TransactionDbSqlDataReader(config.GetConnectionString("ReadOnlyTransactionDbConnectionString"), 600));
            services.AddDbContext<ReadOnlyTransactionDbContext>(options =>
                options.UseSqlServer(config.GetConnectionString("ReadOnlyTransactionDbConnectionString"), SqlResiliencyBuilder), ServiceLifetime.Transient);

            // Repositories
            services.AddScoped<ITransactionRepository, TransactionRepository>();
            services.AddScoped<IFaMasterRepository, FaMasterRepository>();
            services.AddScoped<IRouteRepository, RouteRepository>();
            services.AddScoped<ITaskManagementRepository, TaskManagementRepository>();
            services.AddScoped<IHccbTaskRepository, HccbTaskRepository>();

            // Services
            services.AddScoped<TaskSummaryService>();

            services.AddScoped<IServiceProvider>(s => services.BuildServiceProvider());
            services.AddSlackNet(c => c
                .UseApiToken(config.GetValue<string>("Slack:ApiToken")));
            services.AddSingleton<ISlackLogHelper>(sp => new SlackLogHelperV2(sp.GetRequiredService<IHttpClientFactory>(),
                sp.GetRequiredService<ISlackApiClient>(), masterStorageConnectionString: string.Empty, ChannelId));
            
            services.AddHttpClient();
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.CommandTimeout(300);
            o.EnableRetryOnFailure(3,
                TimeSpan.FromSeconds(30),
                null);
        }
    }
}
