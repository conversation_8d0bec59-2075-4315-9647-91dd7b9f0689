﻿using HCCB.Core.Models;
using HCCB.DbStorage.DbContexts;
using HCCB.DbStorage.DbModel.HccbDbModel;
using HCCB.DbStorage.DbModel.TransactionDbModel;
using Libraries.CommonModels;
using Library.DateTimeHelpers;
using Microsoft.EntityFrameworkCore;
using System.Data.SqlClient;

namespace HCCB.DbStorage.Repositories.TransactionRepositories
{
    public class TransactionRepository : ITransactionRepository
    {
        private readonly TransactionDbSqlDataReader _transactionDbSqlDataReader;
        private readonly ReadOnlyTransactionDbContext _readOnlyTransactionDbContext;
        public TransactionRepository(TransactionDbSqlDataReader transactionDbSqlDataReader, ReadOnlyTransactionDbContext readOnlyTransactionDbContext)
        {
            _transactionDbSqlDataReader = transactionDbSqlDataReader;
            _readOnlyTransactionDbContext = readOnlyTransactionDbContext;
        }

        public async Task<List<long>> GetDateKeysForNewData(long companyId, DateTime startTime, DateTime endTime)
        {
            var sql = $@"Select Distinct DateKey Id
                      from TaskAchievementAndRewards TAR 
                      where CompanyId = @companyId and CreatedAt between @startTime and @endTime";

            var sqlParameters = new List<SqlParameter>
            {
                new SqlParameter("@companyId",companyId),
                new SqlParameter("@startTime",startTime),
                new SqlParameter("@endTime",endTime),
            };
            return (await _transactionDbSqlDataReader.GetModelFromQueryAsync<EntityMinlong>(sql, parameters: sqlParameters)).Select(e => e.Id).ToList();
        }

        public async Task<List<HccbTaskAchievementAndReward>> GetTaskAchievementAndRewards(long companyId, long dateKey)
        {
            var sql = $@"Select TAR.TaskId,TAR.TaskEntityId,TAR.TaskTarget,TAR.TaskAchievement,TAR.RewardId,TAR.RewardQuantity,TAR.IsCompleted,TAR.FaEventId,TAR.CompanyId
                      ,TAR.CreatedAt,TAR.SyncedAt,TAR.DateKey,TAR.ParentId,TAR.ParentType,TAR.TaskAchievementValue,Dr.RouteId,DR.EmployeeId from 
                      DayRecords DR inner join FieldEvents FE on DR.SessionId = FE.SessionId inner join
                      TaskAchievementAndRewards TAR on FE.Id = TAR.FaEventId where DR.CompanyId = @companyId and Dr.IsInvalid = 0 and Dr.QualifiedDate = @dateKey 
                      and FE.CompanyId = @companyId and TAR.CompanyId = @companyId and TAR.DateKey = @dateKey";

            var sqlParameters = new List<SqlParameter>
            {
                new SqlParameter("@companyId",companyId),
                new SqlParameter("@dateKey",dateKey),
            };
            return (await _transactionDbSqlDataReader.GetModelFromQueryAsync<HccbTaskAchievementAndReward>(sql, parameters: sqlParameters)).ToList();
        }

        public async Task<List<OutletCreationRequestData>> GetOutletCreationRequests(long companyId, DateTime startTime, DateTime endTime)
        {
            var sql = $@"Select R.OwnersNo, R.EmployeeId, R.DeviceTime, FE.RequestId
                         from OutletCreationRequests R
                         inner join FieldEvents FE on R.FieldEventId = FE.Id
                         where R.CompanyId = @companyId and R.DeviceTime between @startTime and @endTime";

            var sqlParameters = new List<SqlParameter>
            {
                new SqlParameter("@companyId", companyId),
                new SqlParameter("@startTime", startTime),
                new SqlParameter("@endTime", endTime),
            };
            return (await _transactionDbSqlDataReader.GetModelFromQueryAsync<OutletCreationRequestData>(sql, parameters: sqlParameters)).ToList();
        }
        public async Task<AssetAllocation> GetAssetAllocationRequestById(long id, long companyId)
        {
            return await _readOnlyTransactionDbContext.AssetAllocations.Where(r => r.CompanyId == companyId && r.Id == id).FirstOrDefaultAsync();
        }

        public async Task<AssetReallocationRequest> GetAssetRellocationRequestById(long id, long companyId)
        {
            return await _readOnlyTransactionDbContext.AssetReallocationRequests
                .Where(r => r.CompanyId == companyId && r.Id == id)
                .FirstOrDefaultAsync();
        }
    }
}
