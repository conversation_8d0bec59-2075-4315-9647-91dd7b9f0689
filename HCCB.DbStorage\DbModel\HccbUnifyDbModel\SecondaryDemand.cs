﻿using Core.Models;
using Core.Models.DTOs;
using HCCB.DbStorage.DbModel.HccbDbModel;
using Libraries.CommonEnums;
using Library.DateTimeHelpers;

namespace HCCB.DbStorage.DbModel.HccbUnifyDbModel
{
    public class SecondaryDemand
    {
        public SecondaryDemand(HccbOrder order, FA_MTD_LMTD mTDLMTD, PositionUserHierarchy positionUserHierarchy, LocationMinModel locationMinModel, GeoHierarchy geoHierarchy, 
            ProductFlat productFlat)
        {
            CompanyId = (int)order.CompanyId;
            OrderDate = order.DocumentDate;
            OrderDateKey = (int)order.DocumentDate.GetDateKey();
            FieldUserId = (int)order.ClientEmployee_Id;
            OrderNumber = order.DocumentNumber_Header;
            OutletId = order.F2kLocations_Id;
            ProductId = order.FaCompanyProducts_Id;
            MRPOrder = (decimal)order.Mrp_Ea;
            DistributorId = (int?)order.FaDistributors_Id ?? 0;

            OrderGrossValue = (decimal)order.LineNetAmount;
            OrderNetValue = (decimal)order.LineNetAmount;
            OrderTotalValue = (decimal)order.LineNetAmount;

            OrderQtyInUnit = order.Qty_Each;
            OrderQtyInStdUnit = order.Qty_Cs;
            OrderQtyInSuperUnit = 0;
            var daysDifference = (order.DocumentDate - mTDLMTD.MTD.StartDate).Days + 1;

            OrderMonth = (byte)mTDLMTD.MTD.MonthNumber;
            OrderYear = (byte)mTDLMTD.MTD.YearNumber;
            OrderWeek = (byte)(daysDifference == 0 ? 1 :
                daysDifference % 7 == 0 ? daysDifference / 7 :
                daysDifference / 7 +
                1);

            IsProductive = true;
            OrderSource = order.OrderSource;
            CreatedAt = DateTime.UtcNow;

            FieldUserPosition = (int)positionUserHierarchy.PositionId;
            PositionL2UserId = (int)positionUserHierarchy.UserLevel2Id;
            PositionL3UserId = (int)positionUserHierarchy.UserLevel3Id;
            PositionL4UserId = (int)positionUserHierarchy.UserLevel4Id;
            PositionL5UserId = (int)positionUserHierarchy.UserLevel5Id;
            PositionL6UserId = (int)positionUserHierarchy.UserLevel6Id;
            PositionL7UserId = (int)positionUserHierarchy.UserLevel7Id;
            PositionL8UserId = (int)positionUserHierarchy.UserLevel8Id;
            L2PositionId = (int)positionUserHierarchy.PositionLevel2Id;
            L3PositionId = (int)positionUserHierarchy.PositionLevel3Id;
            L4PositionId = (int)positionUserHierarchy.PositionLevel4Id;
            L5PositionId = (int)positionUserHierarchy.PositionLevel5Id;
            L6PositionId = (int)positionUserHierarchy.PositionLevel6Id;
            L7PositionId = (int)positionUserHierarchy.PositionLevel7Id;
            L8PositionId = (int)positionUserHierarchy.PositionLevel8Id;
            UserPositionLevel = positionUserHierarchy.Level;
            UserPositionName = positionUserHierarchy.PositionName;

            OutletCategory = locationMinModel.OutletCategory ?? 0;
            OutletChannel = locationMinModel.OutletChannel ?? 0;
            OutletSegmentation = locationMinModel.Segmentation;
            IsFocussedOutlet = locationMinModel.IsFocused;
            BeatId = locationMinModel.BeatId;
            //OutletTag = TODO;

            TerritoryId = (int)geoHierarchy.TerritoryId;
            RegionId = (int)geoHierarchy.RegionId;
            ZoneId = (int)geoHierarchy.ZoneId;
            L5GeoId = (int)geoHierarchy.Level5GeoId;
            L6GeoId = (int)geoHierarchy.Level6GeoId;
            L7GeoId = (int)geoHierarchy.Level7GeoId;

            SecondaryCategoryId = (int)productFlat.SecondaryCategoryId;
            PrimaryCategoryId = (int)productFlat.PrimaryCategoryId;
            ProductDivisionId = (int)productFlat.ProductDivisionId;
            DisplayCategoryId = (int)productFlat.DisplayCategoryId;
            AlternateCategory = productFlat.AlternateCategory;
            ProductAttributeText1 = productFlat.ProductAttributeText1;
            ProductAttributeText2 = productFlat.ProductAttributeText2;
        }

        public byte IsDeleted { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public long AttendanceId { get; set; }
        public string OrderNumber { get; set; }
        public DateTime? CallStartTime { get; set; }
        public int CallStartDateKey { get; set; }
        public DateTime? CallEndTime { get; set; }
        public float ActivityCallDuration { get; set; }
        public bool IsTelephonic { get; set; }
        public DateTime? PhoneCallStartTime { get; set; }
        public float PhoneCallDuration { get; set; }
        public bool IsScheduled { get; set; }
        public bool IsOVC { get; set; }
        public bool IsOVT { get; set; }
        public bool IsJW { get; set; }
        public int JWManagerId { get; set; }
        public int JWMManagerPositionId { get; set; }
        public string OrderSource { get; set; }
        public DateTime OrderDate { get; set; }
        public int OrderDateKey { get; set; }
        public byte OrderWeek { get; set; }
        public byte OrderMonth { get; set; }
        public ushort OrderYear { get; set; }
        public int FieldUserId { get; set; }
        public int FieldUserPosition { get; set; }
        public int PositionL2UserId { get; set; }
        public int L2PositionId { get; set; }
        public int PositionL3UserId { get; set; }
        public int L3PositionId { get; set; }
        public int PositionL4UserId { get; set; }
        public int L4PositionId { get; set; }
        public int PositionL5UserId { get; set; }
        public int L5PositionId { get; set; }
        public int PositionL6UserId { get; set; }
        public int L6PositionId { get; set; }
        public int PositionL7UserId { get; set; }
        public int L7PositionId { get; set; }
        public int PositionL8UserId { get; set; }
        public int L8PositionId { get; set; }
        public long OutletId { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public bool IsProductive { get; set; }
        public string NoSalesReason { get; set; }
        public List<string> OutletTag { get; set; }
        public int OutletSegmentation { get; set; }
        public bool IsNewOutlet { get; set; }
        public bool IsFocussedOutlet { get; set; }
        public long BeatId { get; set; }
        public int TerritoryId { get; set; }
        public int RegionId { get; set; }
        public int ZoneId { get; set; }
        public int L5GeoId { get; set; }
        public int L6GeoId { get; set; }
        public int L7GeoId { get; set; }
        public long ProductId { get; set; }
        public decimal MRPOrder { get; set; }
        public decimal PTR { get; set; }
        public decimal OriginalPTR { get; set; }
        public bool IsMustSell { get; set; }
        public bool IsFocussedProduct { get; set; }
        public bool IsRecommended { get; set; }
        public long ProductRecommendationId { get; set; }
        public int RecommendationTypeTag { get; set; }
        public float RecommendedQty { get; set; }
        public bool IsPromoted { get; set; }
        public bool IsFastMoving { get; set; }
        public bool IsUpSell { get; set; }
        public bool IsAssorted { get; set; }
        public int ProductDivisionId { get; set; }
        public int PrimaryCategoryId { get; set; }
        public int SecondaryCategoryId { get; set; }
        public int DisplayCategoryId { get; set; }
        public string AlternateCategory { get; set; }
        public string ProductAttributeText1 { get; set; }
        public string ProductAttributeText2 { get; set; }
        public string OrderStatus { get; set; }
        public decimal OrderQtyInUnit { get; set; }
        public decimal OrderQtyInStdUnit { get; set; }
        public decimal OrderQtyInSuperUnit { get; set; }
        public decimal OrderGrossValue { get; set; }
        public decimal TotalTradeDiscountInOrder { get; set; }
        public decimal PrimarySchemeDiscountInOrder { get; set; }
        public decimal SecondarySchemeDiscountInOrder { get; set; }
        public decimal TotalDiscountInOrder { get; set; }
        public decimal CashDiscountInOrder { get; set; }
        public decimal OrderCGST { get; set; }
        public decimal OrderIGST { get; set; }
        public decimal OrderSGST { get; set; }
        public decimal OrderNetValue { get; set; }
        public bool IsOrderCancelled { get; set; }
        public decimal CancelledOrderQtyInUnit { get; set; }
        public decimal CancelledOrderQtyInStdUnit { get; set; }
        public decimal CancelledOrderQtyInSuperUnit { get; set; }
        public decimal CancelledOrderValue { get; set; }
        public int DistributorId { get; set; }
        public int SuperStockistId { get; set; }
        public string StockistType { get; set; }
        public string WarehouseErpId { get; set; }
        public string State { get; set; }
        public int DistributorRegionId { get; set; }
        public int DistributorZoneId { get; set; }
        public int DistributorChannelId { get; set; }
        public int? DistributorSegmentationId { get; set; }
        public byte DistributorUserRole { get; set; }
        public string BatchNo { get; set; }
        public bool IsSecondaryOrderValidationDone { get; set; }
        public bool IsAssetPresent { get; set; }
        public int ReportingManagerId { get; set; }
        public PositionCodeLevel UserPositionLevel { get; set; }
        public string UserPositionName { get; set; }
        public int DesignationId { get; set; }
        public long ModifyTimeStamp { get; set; }
        public long? ShopTypeId { get; set; }
        public int OutletChannel { get; set; }
        public int OutletCategory { get; set; }
        public string Variant { get; set; }
        public decimal MarginValue { get; set; }
        public string DistributorAddress { get; set; }
        public decimal OrderCESSPercentage { get; set; }
        public decimal OrderCESSValue { get; set; }
        public decimal OrderTCSPercentage { get; set; }
        public decimal OrderTCSValue { get; set; }
        public decimal OrderAdditionalDiscountPercentage1 { get; set; }
        public decimal OrderAdditionalDiscountValue1 { get; set; }
        public decimal OrderAdditionalDiscountPercentage2 { get; set; }
        public decimal OrderAdditionalDiscountValue2 { get; set; }
        public decimal OrderGSTPercentage { get; set; }
        public decimal OrderGSTValue { get; set; }
        public decimal OrderIGSTPercentage { get; set; }
        public decimal OrderCGSTPercentage { get; set; }
        public decimal OrderSGSTPercentage { get; set; }
        public decimal OrderUGSTPercentage { get; set; }
        public decimal OrderUGSTValue { get; set; }
        public decimal OrderFOCPercentage { get; set; }
        public decimal OrderFOCValue { get; set; }
        public decimal FreeQtyinUnit { get; set; }
        public decimal OrderVATPercentage { get; set; }
        public decimal OrderVATValue { get; set; }
        public int BasketId { get; set; }
        public string BasketErpId { get; set; }
        public decimal BasketQuantity { get; set; }
        public string Color { get; set; }
        public string ProductSize { get; set; }
        public int ManagerPositionId { get; set; }
        public decimal OrderTotalValue { get; set; }
        public decimal OrderStdUnitConversionFactor { get; set; }
        public decimal OrderSuperUnitConversionFactor { get; set; }
        public long RouteId { get; set; }
        public string NoSalesReasonCategory { get; set; }
        public string MasterBatchId { get; set; }
        public decimal PTD { get; set; }
        public long? ProductGroupID { get; set; }
        public decimal TotalSchemeDiscountInOrder { get; set; }
    }
}
