﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HCCBTaskStatusProcessor.Configurations;
using HCCBTaskStatusProcessor.Services;

namespace HCCBTaskStatusProcessor
{
    internal static class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = new HostBuilder()
               .ConfigureAppConfiguration((hostingContext, config) =>
               {
                   config.AddJsonFile($"appsettings.json", true, true)
                         .AddEnvironmentVariables();
               })
              .ConfigureServices((context, services) =>
              {
                  Dependencies.SetUp(services, context.Configuration);
              })
              .UseConsoleLifetime();
            var host = builder.Build();
            using (host)
            {
                await host.Services.GetRequiredService<TaskStatusProcessor>().Process();
            }
        }
    }
}