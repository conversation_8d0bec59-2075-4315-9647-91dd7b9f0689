﻿
namespace HCCB.DbStorage.DbModel.MasterDbModel
{
    public class TaskManagementUserFocusArea
    {
        public long Id { get; set; }
        public long? TaskManagementFocusAreaID { get; set; }
        public long CompanyId { get; set; }
        public long? EntityId { get; set; }
        public string Description { get; set; }
        public bool? IsDeactive { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime? LastUpdatedAt { get; set; }
    }
}
