﻿using Libraries.CommonEnums;

namespace Core.Models
{
    public class PositionUserHierarchy
    {
        public long PositionId { get; set; }
        public long UserId { get; set; }
        public long PositionLevel2Id { get; set; }
        public long PositionLevel3Id { get; set; }
        public long PositionLevel4Id { get; set; }
        public long PositionLevel5Id { get; set; }
        public long PositionLevel6Id { get; set; }
        public long PositionLevel7Id { get; set; }
        public long PositionLevel8Id { get; set; }
        public long UserLevel2Id { get; set; }
        public long UserLevel3Id { get; set; }
        public long UserLevel4Id { get; set; }
        public long UserLevel5Id { get; set; }
        public long UserLevel6Id { get; set; }
        public long UserLevel7Id { get; set; }
        public long UserLevel8Id { get; set; }
        public PositionCodeLevel Level { get; set; }
        public string PositionName { get; set; }
    }
}