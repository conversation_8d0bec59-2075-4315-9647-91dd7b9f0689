﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.1.1" />
    <PackageReference Include="MSTest.TestFramework" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CueCard-ADLS-Data\CueCard-ADLS-Data.csproj" />
    <ProjectReference Include="..\HCCB-ADLS-DataConnector\HCCB-ADLS-DataConnector.csproj" />
    <ProjectReference Include="..\HCCB-ADLS-Triggered\HCCB-ADLS-Triggered.csproj" />
    <ProjectReference Include="..\HCCB.FailedOrderResyncJob\HCCB.FailedOrderResyncJob.csproj" />
    <ProjectReference Include="..\HCCBBlobTriggeredSubscriber\HCCBBlobTriggeredSubscriber.csproj" />
    <ProjectReference Include="..\HCCBCtsApprovalProcessor\HCCBCtsApprovalProcessor.csproj" />
    <ProjectReference Include="..\CTSStatusProcessor\CTSStatusProcessor.csproj" />
    <ProjectReference Include="..\HCCBTaskStatusProcessor\HCCBTaskStatusProcessor.csproj" />
    <ProjectReference Include="..\HCCBTaskSummary\HCCBTaskSummary.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Microsoft.VisualStudio.TestTools.UnitTesting" />
  </ItemGroup>

</Project>
