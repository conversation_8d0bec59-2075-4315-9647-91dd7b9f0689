﻿using Core.Models.DTOs;
using Core.Repositories;
using HCCB.DbStorage.DbContexts;
using System.Data.SqlClient;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public class ProductsRepository : IProductsRepository
    {
        private readonly MasterDbSqlDataReader _masterDbSqlDataReader;

        public ProductsRepository(MasterDbSqlDataReader masterDbSqlDataReader)
        {
            _masterDbSqlDataReader=masterDbSqlDataReader;
        }

        public async Task<List<ProductFlat>> GetProductHierarchy(long companyId)
        {
            var sql = $@"SELECT
                        p.Id ProductId,
                        sc.Id SecondaryCategoryId,
                        pc.Id PrimaryCategoryId,
                        pd.Id ProductDivisionId,
                        p.Product_AttributeText1 ProductAttributeText1,
                        p.Product_AttributeText2 ProductAttributeText2,
                        p.ProductDisplayCategoryId DisplayCategoryId,
                        p.Category1 AlternateCategory
                        from FACompanyProducts p
                        JOIN FAProductSecondaryCategory sc on sc.Id = p.ProductCategoryId
                        JOIN FAProductPrimaryCategory pc on pc.Id = sc.ProductPrimaryCategoryId
                        JOIN FAProductDivision pd on pd.Id = pc.ProductDivisionId
                        where p.CompanyId = @companyId
                        and sc.CompanyId = @companyId
                        and pc.CompanyId = @companyId
                        and pd.CompanyId = @companyId";

            var sqlParameters = new List<SqlParameter> { new SqlParameter("companyId", companyId) };

            var response = await _masterDbSqlDataReader.GetModelFromQueryAsync<ProductFlat>(sql, parameters: sqlParameters, commandTimeout: 1200);

            return response.ToList();
        }
    }
}
