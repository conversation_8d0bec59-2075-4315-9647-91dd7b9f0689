﻿using HCCB.DbStorage.DbModel.MasterDbModel;

namespace HCCB.DbStorage.Repositories.MasterRepositories
{
    public interface IFaMasterRepository
    {
        Task<Dictionary<string, long>> GetEmployeesAsync(long companyId);
        Task<string> GetEmployeeErpById(long id);
        Task<List<long>> GetActiveEmployeeIds(long companyId);

        Task<Dictionary<string, long>> GetDistributorsAsync(long companyId);

        Task<Dictionary<string, long>> GetLocationsAsync(long companyId);
        Task<List<Location>> GetOutletsByIds(List<long> list);

        Task<Dictionary<string, Product>> GetProductsAsync(long companyId);
        Task<List<string>> GetActiveOutletMarginNames(long companyId);
        Task<string> GetAssetDefinitionErpIdByName(string assestName, long companyId);
        Task<string> GetAssetErpIdByAssetMappingId(long assetMappingId, long companyId);
        Task<List<long>> GetPositionIdsForUserId(long userId, long companyId);
    }
}
