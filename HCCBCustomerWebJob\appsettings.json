{"Environment": "local", "CompanyId": 193017, "ConnectionStrings": {"TransactionDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=FA_Transactions_GT;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;", "StorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=faappapiv3beta;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "HCCBFaDataConnectionString": "Server=tcp:pygwentyrn.database.windows.net,1433;Initial Catalog=hccb-fa-data;Persist Security Info=False;User ID=hccbproductrecom;Password=***********************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=100;", "ReadOnlyMasterDbConnectionString": "data source=pygwentyrn.database.windows.net;initial catalog=F2KLocationsNetworkV3;user id=KeyVaultReadOnly;password=*******$Uuxwp7Mcxo7Khy;", "MasterStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=locationsnetwork;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net;"}, "hccbAPIhelper": {"userName": "gOtRlf2XBkse4ldJebERSX", "password": "RvjmjroMe", "salesAreainterfaceId": 2, "masterInterfaceId": 1, "companyId": 193017, "partnerId": 1, "HCCBClientAPIToken": "SENDQl9GaWVsZFRlc3RfVUFUOldpVGNMQVdiaWl6cl40Rm9xZlBT", "APIBasePath": "https://fa-external-apis-release-testing.azurewebsites.net", "HccbBaseUrl": "https://emw1-apigw.dm-em.informaticacloud.com/t/0i8me60sbullrpgbyrzuuy.com", "ExtApiBasePath": "https://api-debug-enterprise-1.fieldassist.io"}}